2025-06-06 16:34:08,473 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN/
2025-06-06 16:34:08,473 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:34:08,473 - INFO - ====== WebDriver manager ======
2025-06-06 16:34:08,557 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:34:08,630 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:34:08,695 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:34:24,897 - INFO - ✅ Connected to existing Chrome session!
2025-06-06 16:34:28,489 - INFO - Scrolling to load comments...
2025-06-06 16:34:51,639 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(54, 'Connection reset by peer')': /session/98ac1b8757b3e5716e6c752bee50bd40
2025-06-06 16:34:51,641 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x104a54cd0>: Failed to establish a new connection: [Errno 61] Connection refused')': /session/98ac1b8757b3e5716e6c752bee50bd40
2025-06-06 16:34:51,642 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x104a54910>: Failed to establish a new connection: [Errno 61] Connection refused')': /session/98ac1b8757b3e5716e6c752bee50bd40
2025-06-06 16:34:51,644 - INFO - WebDriver closed
2025-06-06 16:36:17,730 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN/
2025-06-06 16:36:17,730 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:36:17,731 - INFO - ====== WebDriver manager ======
2025-06-06 16:36:17,815 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:36:17,885 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:36:17,949 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:36:18,327 - INFO - ✅ Connected to existing Chrome session!
2025-06-06 16:36:25,016 - INFO - Extracting comments...
2025-06-06 16:36:30,017 - INFO - Looking for scrollable comment container...
2025-06-06 16:36:30,031 - WARNING - No suitable comment container found
2025-06-06 16:36:30,031 - WARNING - No comment container found
2025-06-06 16:36:30,032 - INFO - Extracted 0 unique comments
2025-06-06 16:36:30,032 - WARNING - No comments found
2025-06-06 16:36:30,054 - INFO - WebDriver closed
2025-06-06 16:37:07,815 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN
2025-06-06 16:37:07,815 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:37:07,815 - INFO - ====== WebDriver manager ======
2025-06-06 16:37:07,947 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:37:08,018 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:37:08,073 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:37:49,208 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN
2025-06-06 16:37:49,208 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:37:49,208 - INFO - ====== WebDriver manager ======
2025-06-06 16:37:49,290 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:37:49,358 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:37:49,416 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
