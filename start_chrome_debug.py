#!/usr/bin/env python3
"""
Helper script to start Chrome with remote debugging enabled
This allows the scraper to connect to your existing Chrome session
"""

import os
import sys
import subprocess
import platform
import time

def find_chrome_executable():
    """Find Chrome executable on different operating systems"""
    system = platform.system()
    
    if system == "Darwin":  # macOS
        chrome_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
            "/Applications/Chrome.app/Contents/MacOS/Chrome"
        ]
    elif system == "Windows":
        chrome_paths = [
            "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
            os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe")
        ]
    else:  # Linux
        chrome_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser",
            "/snap/bin/chromium"
        ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            return path
    
    return None

def is_chrome_running_with_debug():
    """Check if Chrome is already running with remote debugging"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:9222/json", timeout=2)
        if response.status_code == 200:
            return True
    except:
        pass
    return False

def start_chrome_with_debug():
    """Start Chrome with remote debugging enabled"""
    
    # Check if Chrome is already running with debug port
    if is_chrome_running_with_debug():
        print("✅ Chrome is already running with remote debugging enabled!")
        print("🔗 Debug port: http://127.0.0.1:9222")
        return True
    
    # Find Chrome executable
    chrome_path = find_chrome_executable()
    if not chrome_path:
        print("❌ Could not find Chrome executable")
        print("💡 Please install Google Chrome or update the paths in this script")
        return False
    
    print(f"🔍 Found Chrome at: {chrome_path}")
    
    # Chrome arguments for remote debugging
    chrome_args = [
        chrome_path,
        "--remote-debugging-port=9222",
        "--user-data-dir=/tmp/chrome_debug_profile",  # Use separate profile
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-extensions-except",
        "--disable-plugins-discovery"
    ]
    
    try:
        print("🚀 Starting Chrome with remote debugging...")
        print("📝 Chrome will open with a separate debug profile")
        print("🔐 Please log in to Instagram in this Chrome window")
        
        # Start Chrome process
        if platform.system() == "Windows":
            subprocess.Popen(chrome_args, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:
            subprocess.Popen(chrome_args, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # Wait a moment for Chrome to start
        time.sleep(3)
        
        # Verify it's running
        if is_chrome_running_with_debug():
            print("✅ Chrome started successfully with remote debugging!")
            print("🔗 Debug port: http://127.0.0.1:9222")
            print("\n📋 Next steps:")
            print("1. Log in to Instagram in the Chrome window that just opened")
            print("2. Navigate to the Instagram post you want to scrape")
            print("3. Run the scraper: python run_scraper.py")
            return True
        else:
            print("❌ Failed to start Chrome with remote debugging")
            return False
            
    except Exception as e:
        print(f"❌ Error starting Chrome: {e}")
        return False

def main():
    """Main function"""
    print("🌐 Chrome Remote Debug Starter")
    print("=" * 40)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--check":
        # Just check if debug port is available
        if is_chrome_running_with_debug():
            print("✅ Chrome remote debugging is available")
            print("🔗 Debug port: http://127.0.0.1:9222")
        else:
            print("❌ Chrome remote debugging is not available")
            print("💡 Run this script without --check to start Chrome")
        return
    
    success = start_chrome_with_debug()
    
    if success:
        print("\n🎉 Setup complete!")
        print("💡 Keep this Chrome window open while using the scraper")
        print("⚠️  Don't close Chrome or the scraper won't be able to connect")
    else:
        print("\n❌ Setup failed")
        print("💡 You can try running the scraper anyway - it will create a new Chrome session")

if __name__ == "__main__":
    main()
