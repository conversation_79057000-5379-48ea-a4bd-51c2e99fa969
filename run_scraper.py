#!/usr/bin/env python3
"""
Simple script to run the Instagram comment scraper
Usage: python run_scraper.py [URL]
"""

import sys
import os
from instagram_scraper_simple import InstagramCommentScraperSimple

def main():
    # Default URL if none provided
    default_url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    # Get URL from command line argument or use default
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = default_url
        
    print(f"🚀 Starting Instagram Comment Scraper")
    print(f"📱 Target URL: {url}")
    print("=" * 60)
    
    # Initialize scraper (uses existing Chrome session to stay logged in)
    scraper = InstagramCommentScraperSimple(headless=False, use_existing_chrome=True)
    
    try:
        # Run the scraper
        result = scraper.scrape_post(url)
        
        if result:
            print(f"\n✅ SUCCESS!")
            print(f"📄 Comments saved to: {result}")
            
            # Show some statistics
            if os.path.exists(result):
                import csv
                with open(result, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    comments = list(reader)
                    
                print(f"📊 Total items scraped: {len(comments)}")
                
                # Show sample comments
                if comments:
                    print(f"\n📝 Sample content:")
                    for i, comment in enumerate(comments[:5]):
                        username = comment['username']
                        text = comment['comment'][:80] + "..." if len(comment['comment']) > 80 else comment['comment']
                        print(f"  {i+1}. @{username}: {text}")
                        
                    if len(comments) > 5:
                        print(f"  ... and {len(comments) - 5} more items")
                        
        else:
            print("❌ FAILED: No content was scraped")
            print("💡 Try running with visible browser (headless=False) to debug")
            
    except KeyboardInterrupt:
        print("\n⏹️  Scraping interrupted by user")
    except Exception as e:
        print(f"❌ ERROR: {e}")
        print("💡 Check the logs for more details")
    
    print("\n🏁 Scraping completed!")

if __name__ == "__main__":
    main()
