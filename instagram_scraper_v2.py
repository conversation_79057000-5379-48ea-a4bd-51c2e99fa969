"""
Instagram Comment Scraper V2
Improved version with better comment detection
"""

import os
import csv
import time
import random
import logging
import re
import json
from datetime import datetime
from typing import List, Dict, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager

import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper_v2.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class InstagramCommentScraperV2:
    def __init__(self, headless: bool = config.HEADLESS):
        """Initialize the Instagram comment scraper"""
        self.driver = None
        self.wait = None
        self.headless = headless
        self.comments_data = []
        
        # Create output directory
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
            
        # Anti-detection measures
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Set window size
        chrome_options.add_argument(f'--window-size={config.WINDOW_SIZE[0]},{config.WINDOW_SIZE[1]}')
        
        # Random user agent
        user_agent = random.choice(config.USER_AGENTS)
        chrome_options.add_argument(f'--user-agent={user_agent}')
        
        # Initialize driver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Set implicit wait
        self.driver.implicitly_wait(config.IMPLICIT_WAIT)
        self.wait = WebDriverWait(self.driver, config.IMPLICIT_WAIT)
        
        logger.info("WebDriver setup completed")

    def random_delay(self):
        """Add random delay to avoid detection"""
        delay = random.uniform(config.MIN_DELAY, config.MAX_DELAY)
        time.sleep(delay)

    def expand_comment_replies(self):
        """Click on 'View replies' buttons to expand comment threads"""
        logger.info("Expanding comment replies...")

        reply_selectors = [
            "//button[contains(text(), 'View replies')]",
            "//button[contains(text(), 'replies')]",
            "//span[contains(text(), 'View replies')]",
            "//span[contains(text(), 'replies')]",
            "//div[@role='button'][contains(text(), 'replies')]",
            "//button[contains(@aria-label, 'replies')]"
        ]

        expanded_count = 0
        for selector in reply_selectors:
            try:
                reply_buttons = self.driver.find_elements(By.XPATH, selector)
                for button in reply_buttons:
                    try:
                        if button.is_displayed() and button.is_enabled():
                            # Scroll into view
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                            time.sleep(0.5)
                            button.click()
                            expanded_count += 1
                            logger.info(f"Expanded reply thread {expanded_count}")
                            time.sleep(1)  # Wait for replies to load
                    except Exception as e:
                        logger.debug(f"Error clicking reply button: {e}")
                        continue
            except:
                continue

        logger.info(f"Expanded {expanded_count} reply threads")
        
    def find_comment_container(self):
        """Find the scrollable comment container"""
        comment_container_selectors = [
            # Common Instagram comment container patterns
            'div[style*="overflow"]',
            'div[style*="overflow-y: auto"]',
            'div[style*="overflow-x: auto"]',
            'div[style*="overflow: auto"]',
            'div[style*="overflow-y:auto"]',
            'div[style*="overflow-x:auto"]',
            'div[style*="overflow:auto"]',
            # Look for containers with specific heights that might be scrollable
            'div[style*="max-height"]',
            'div[style*="height"][style*="overflow"]',
            # Article containers that might contain comments
            'article div[style*="overflow"]',
            'section div[style*="overflow"]',
        ]

        for selector in comment_container_selectors:
            try:
                containers = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for container in containers:
                    try:
                        # Check if this container has scrollable content
                        scroll_height = self.driver.execute_script("return arguments[0].scrollHeight", container)
                        client_height = self.driver.execute_script("return arguments[0].clientHeight", container)

                        if scroll_height > client_height:
                            logger.info(f"Found scrollable container with selector: {selector}")
                            logger.info(f"Container scroll height: {scroll_height}, client height: {client_height}")
                            return container
                    except:
                        continue
            except:
                continue

        logger.warning("No scrollable comment container found, will use page scroll as fallback")
        return None

    def scroll_to_load_comments(self):
        """Scroll down to load more comments"""
        logger.info("Scrolling to load comments...")

        # First, try to find the comment container
        comment_container = self.find_comment_container()

        if comment_container:
            logger.info("Scrolling within comment container...")
            # Scroll within the comment container
            for i in range(config.CONTAINER_SCROLL_ATTEMPTS):  # More scrolls for container
                try:
                    # Get current scroll position
                    current_scroll = self.driver.execute_script("return arguments[0].scrollTop", comment_container)

                    # Scroll down within the container
                    self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", comment_container)
                    self.random_delay()

                    # Check if scroll position changed
                    new_scroll = self.driver.execute_script("return arguments[0].scrollTop", comment_container)

                    if new_scroll == current_scroll:
                        logger.info(f"Reached end of comment container after {i+1} scrolls")
                        break

                    # Also try to click "Load more comments" buttons
                    try:
                        # Multiple patterns for "Load more" buttons
                        load_more_selectors = [
                            "//button[contains(text(), 'Load more')]",
                            "//button[contains(text(), 'View more')]",
                            "//button[contains(@aria-label, 'Load more')]",
                            "//button[contains(@aria-label, 'View more')]",
                            "//div[@role='button'][contains(text(), 'Load more')]",
                            "//div[@role='button'][contains(text(), 'View more')]",
                            "//span[contains(text(), 'Load more comments')]",
                            "//span[contains(text(), 'View replies')]",
                            # Instagram specific patterns
                            "//button[contains(@aria-label, 'replies')]",
                            "//button[contains(@aria-label, 'comments')]"
                        ]

                        for selector in load_more_selectors:
                            buttons = self.driver.find_elements(By.XPATH, selector)
                            for button in buttons:
                                try:
                                    if button.is_displayed() and button.is_enabled():
                                        # Scroll the button into view first
                                        self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                                        time.sleep(0.5)
                                        button.click()
                                        logger.info(f"Clicked button with selector: {selector}")
                                        time.sleep(2)
                                        break
                                except:
                                    continue
                            if buttons:  # If we found and tried buttons with this selector, break
                                break
                    except Exception as e:
                        logger.debug(f"Error clicking load more buttons: {e}")
                        pass

                except Exception as e:
                    logger.debug(f"Error scrolling container: {e}")
                    break

            logger.info(f"Completed {i+1} container scrolls")

        else:
            # Fallback to page scrolling
            logger.info("Using page scroll as fallback...")
            for i in range(config.MAX_SCROLLS):
                # Scroll down the page
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                self.random_delay()

                # Try to click "Load more comments" buttons
                try:
                    load_more_buttons = self.driver.find_elements(By.XPATH,
                        "//button[contains(text(), 'Load more') or contains(text(), 'View more') or contains(@aria-label, 'Load more')]")
                    for button in load_more_buttons:
                        if button.is_displayed() and button.is_enabled():
                            button.click()
                            logger.info("Clicked 'Load more comments' button")
                            time.sleep(2)
                            break
                except:
                    pass

                # Check if we've reached the end
                current_height = self.driver.execute_script("return document.body.scrollHeight")
                if i > 0 and current_height == getattr(self, 'last_height', 0):
                    logger.info("Reached end of page")
                    break
                self.last_height = current_height

            logger.info(f"Completed {i+1} page scrolls")
        
    def extract_from_page_source(self) -> List[Dict[str, str]]:
        """Extract comments from page source using regex patterns"""
        comments = []
        page_source = self.driver.page_source
        
        try:
            # Pattern 1: Look for username and text patterns in the HTML
            # Instagram often has patterns like: username followed by comment text
            
            # Find potential username patterns
            username_patterns = [
                r'href="https://www\.instagram\.com/([^/"]+)/"[^>]*>([^<]+)</a>',
                r'href="/([^/"]+)/"[^>]*>([^<]+)</a>',
                r'"username":"([^"]+)"',
            ]
            
            usernames_found = set()
            for pattern in username_patterns:
                matches = re.findall(pattern, page_source)
                for match in matches:
                    if isinstance(match, tuple):
                        username = match[0] if len(match) > 0 else match
                        usernames_found.add(username)
                    else:
                        usernames_found.add(match)
            
            logger.info(f"Found {len(usernames_found)} potential usernames in page source")
            
            # Pattern 2: Look for comment text patterns
            comment_patterns = [
                r'"text":"([^"]{10,})"',  # JSON text field with at least 10 chars
                r'<span[^>]*>([^<]{10,})</span>',  # Span content with at least 10 chars
                r'dir="auto">([^<]{10,})</span>',  # Auto-direction spans
            ]
            
            comments_text = set()
            for pattern in comment_patterns:
                matches = re.findall(pattern, page_source)
                for match in matches:
                    text = match.strip()
                    if (len(text) > 10 and 
                        not any(skip in text.lower() for skip in 
                               ['follow', 'like', 'share', 'view', 'ago', 'hours', 'minutes', 'instagram', 'meta'])):
                        comments_text.add(text)
            
            logger.info(f"Found {len(comments_text)} potential comment texts")
            
            # Try to create username-comment pairs
            usernames_list = list(usernames_found)
            comments_list = list(comments_text)
            
            # Simple pairing approach - this could be improved
            for i, comment_text in enumerate(comments_list):
                username = usernames_list[i % len(usernames_list)] if usernames_list else "Unknown"
                
                comments.append({
                    'username': username,
                    'username_link': f"https://instagram.com/{username}",
                    'comment': comment_text,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                
        except Exception as e:
            logger.error(f"Error extracting from page source: {e}")
            
        return comments
    
    def extract_with_selenium(self) -> List[Dict[str, str]]:
        """Extract comments using Selenium element finding"""
        comments = []
        
        try:
            # Find all text elements that might be comments
            text_elements = self.driver.find_elements(By.XPATH, "//span[string-length(text()) > 10]")
            logger.info(f"Found {len(text_elements)} text elements")
            
            for element in text_elements:
                try:
                    text = element.text.strip()
                    if (len(text) > 10 and 
                        not any(skip in text.lower() for skip in 
                               ['follow', 'like', 'share', 'view', 'ago', 'hours', 'minutes', 'instagram', 'meta'])):
                        
                        # Try to find associated username
                        username = "Unknown"
                        
                        # Look for nearby links that might be usernames
                        try:
                            parent = element.find_element(By.XPATH, "..")
                            links = parent.find_elements(By.TAG_NAME, "a")
                            
                            for link in links:
                                href = link.get_attribute("href")
                                if href and "instagram.com/" in href:
                                    username_candidate = href.split("/")[-1] if href.endswith("/") else href.split("/")[-1]
                                    if username_candidate and len(username_candidate) < 30:
                                        username = username_candidate
                                        break
                        except:
                            pass
                        
                        comments.append({
                            'username': username,
                            'username_link': f"https://instagram.com/{username}" if username != "Unknown" else "",
                            'comment': text,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                        
                except Exception as e:
                    continue
                    
        except Exception as e:
            logger.error(f"Error with Selenium extraction: {e}")
            
        return comments
    
    def extract_comments(self) -> List[Dict[str, str]]:
        """Extract comments using multiple approaches"""
        logger.info("Extracting comments...")
        all_comments = []
        
        # Wait for page to load
        time.sleep(config.COMMENT_LOAD_WAIT)
        
        # Approach 1: Page source analysis
        try:
            source_comments = self.extract_from_page_source()
            all_comments.extend(source_comments)
            logger.info(f"Page source approach found {len(source_comments)} comments")
        except Exception as e:
            logger.error(f"Page source approach failed: {e}")
        
        # Approach 2: Selenium element finding
        try:
            selenium_comments = self.extract_with_selenium()
            all_comments.extend(selenium_comments)
            logger.info(f"Selenium approach found {len(selenium_comments)} comments")
        except Exception as e:
            logger.error(f"Selenium approach failed: {e}")
        
        # Remove duplicates
        seen_comments = set()
        unique_comments = []
        
        for comment in all_comments:
            comment_id = f"{comment['username']}:{comment['comment'][:50]}"
            if comment_id not in seen_comments:
                unique_comments.append(comment)
                seen_comments.add(comment_id)
        
        logger.info(f"Extracted {len(unique_comments)} unique comments")
        return unique_comments
        
    def save_to_csv(self, comments: List[Dict[str, str]], filename: str = None):
        """Save comments to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"instagram_comments_v2_{timestamp}.csv"
            
        filepath = os.path.join(config.OUTPUT_DIR, filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['username', 'username_link', 'comment', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for comment in comments:
                    writer.writerow(comment)
                    
            logger.info(f"Comments saved to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
            
    def scrape_post(self, url: str) -> Optional[str]:
        """Main method to scrape comments from an Instagram post"""
        logger.info(f"Starting to scrape: {url}")
        
        try:
            # Setup driver
            self.setup_driver()
            
            # Navigate to the post
            self.driver.get(url)
            self.random_delay()
            
            # Scroll to load comments
            self.scroll_to_load_comments()

            # Expand reply threads to get more comments
            self.expand_comment_replies()

            # Extract comments
            comments = self.extract_comments()
            
            if comments:
                # Save to CSV
                csv_path = self.save_to_csv(comments)
                logger.info(f"Successfully scraped {len(comments)} comments")
                return csv_path
            else:
                logger.warning("No comments found")
                return None
                
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver closed")


def main():
    """Main function for testing"""
    test_url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    scraper = InstagramCommentScraperV2(headless=False)
    result = scraper.scrape_post(test_url)
    
    if result:
        print(f"✅ Comments successfully scraped and saved to: {result}")
    else:
        print("❌ Failed to scrape comments")


if __name__ == "__main__":
    main()
