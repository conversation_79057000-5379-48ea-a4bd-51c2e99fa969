#!/usr/bin/env python3
"""
Setup script for Instagram Comment Scraper
This script helps set up the environment and dependencies
"""

import os
import sys
import subprocess

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False

def main():
    print("🚀 Instagram Comment Scraper Setup")
    print("=" * 40)
    
    # Check if Python is available
    try:
        python_version = subprocess.check_output([sys.executable, "--version"], text=True).strip()
        print(f"✅ Python found: {python_version}")
    except:
        print("❌ Python not found. Please install Python 3.7+")
        return
    
    # Create virtual environment if it doesn't exist
    if not os.path.exists("venv"):
        print("📦 Creating virtual environment...")
        if not run_command(f"{sys.executable} -m venv venv", "Virtual environment creation"):
            return
    else:
        print("✅ Virtual environment already exists")
    
    # Determine activation command based on OS
    if os.name == 'nt':  # Windows
        activate_cmd = "venv\\Scripts\\activate"
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        activate_cmd = "source venv/bin/activate"
        pip_cmd = "venv/bin/pip"
    
    # Install dependencies
    print("📚 Installing dependencies...")
    install_cmd = f"{pip_cmd} install -r requirements.txt"
    if not run_command(install_cmd, "Dependencies installation"):
        return
    
    # Create output directory
    if not os.path.exists("output"):
        os.makedirs("output")
        print("✅ Created output directory")
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Activate the virtual environment:")
    if os.name == 'nt':
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    
    print("2. Run the scraper:")
    print("   python run_scraper.py")
    print("   or")
    print("   python run_scraper.py <instagram_url>")
    
    print("\n📖 For more information, see README.md")

if __name__ == "__main__":
    main()
