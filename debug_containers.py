#!/usr/bin/env python3
"""
Debug script to analyze Instagram containers and find the right one
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def setup_driver():
    """Setup Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def analyze_containers(driver):
    """Analyze all containers on the page"""
    print("🔍 Analyzing all containers...")
    
    # Get detailed container info using JavaScript
    container_info = driver.execute_script("""
        var containers = [];
        var allDivs = document.querySelectorAll('div');
        
        for (var i = 0; i < allDivs.length; i++) {
            var div = allDivs[i];
            var style = window.getComputedStyle(div);
            
            // Get all containers with any overflow setting
            if (style.overflow !== 'visible' || style.overflowY !== 'visible' || style.overflowX !== 'visible') {
                var spans = div.querySelectorAll('span');
                var links = div.querySelectorAll('a');
                var instagramLinks = div.querySelectorAll('a[href*="instagram.com/"]');
                
                containers.push({
                    index: i,
                    overflow: style.overflow,
                    overflowX: style.overflowX,
                    overflowY: style.overflowY,
                    scrollHeight: div.scrollHeight,
                    clientHeight: div.clientHeight,
                    offsetHeight: div.offsetHeight,
                    spanCount: spans.length,
                    linkCount: links.length,
                    instagramLinkCount: instagramLinks.length,
                    className: div.className,
                    id: div.id,
                    isScrollable: div.scrollHeight > div.clientHeight,
                    hasContent: spans.length > 0 || links.length > 0
                });
            }
        }
        
        return containers;
    """)
    
    print(f"Found {len(container_info)} containers with overflow settings")
    
    # Filter and sort containers
    scrollable_containers = [c for c in container_info if c['isScrollable']]
    content_containers = [c for c in container_info if c['spanCount'] > 5]
    instagram_containers = [c for c in container_info if c['instagramLinkCount'] > 0]
    
    print(f"📊 Container Analysis:")
    print(f"   - Scrollable containers: {len(scrollable_containers)}")
    print(f"   - Containers with content (>5 spans): {len(content_containers)}")
    print(f"   - Containers with Instagram links: {len(instagram_containers)}")
    
    # Show top candidates
    candidates = [c for c in container_info if c['isScrollable'] and c['spanCount'] > 3]
    candidates.sort(key=lambda x: x['spanCount'] + x['instagramLinkCount'], reverse=True)
    
    print(f"\n🎯 Top {min(5, len(candidates))} container candidates:")
    for i, container in enumerate(candidates[:5]):
        print(f"\n   Candidate {i+1}:")
        print(f"      Index: {container['index']}")
        print(f"      Overflow: {container['overflow']} / X:{container['overflowX']} / Y:{container['overflowY']}")
        print(f"      Size: {container['scrollHeight']} scroll / {container['clientHeight']} client")
        print(f"      Content: {container['spanCount']} spans, {container['instagramLinkCount']} IG links")
        print(f"      Class: {container['className'][:50]}...")
        print(f"      Scrollable: {container['isScrollable']}")
        
        # Test scrolling this container
        if container['isScrollable']:
            test_scroll_container(driver, container['index'])
    
    return candidates

def test_scroll_container(driver, container_index):
    """Test scrolling a specific container"""
    try:
        # Get the container element
        container = driver.execute_script(f"return document.querySelectorAll('div')[{container_index}]")
        
        if container:
            # Get initial scroll position
            initial_scroll = driver.execute_script("return arguments[0].scrollTop", container)
            
            # Try to scroll
            driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", container)
            time.sleep(1)
            
            # Check new position
            final_scroll = driver.execute_script("return arguments[0].scrollTop", container)
            
            print(f"      Scroll test: {initial_scroll} → {final_scroll} ({'✅ SCROLLED' if final_scroll > initial_scroll else '❌ NO SCROLL'})")
            
            # Reset scroll position
            driver.execute_script("arguments[0].scrollTop = 0", container)
            
    except Exception as e:
        print(f"      Scroll test: ❌ ERROR - {e}")

def extract_sample_content(driver, container_index):
    """Extract sample content from a container"""
    try:
        container = driver.execute_script(f"return document.querySelectorAll('div')[{container_index}]")
        
        if container:
            # Get sample spans and links
            spans = container.find_elements(By.TAG_NAME, "span")
            links = container.find_elements(By.CSS_SELECTOR, "a[href*='instagram.com/']")
            
            print(f"\n   📝 Sample content from container {container_index}:")
            
            # Show sample spans
            print(f"      Sample spans:")
            for i, span in enumerate(spans[:3]):
                try:
                    text = span.text.strip()
                    if text and len(text) > 3:
                        print(f"         {i+1}. {text[:60]}...")
                except:
                    continue
            
            # Show sample links
            print(f"      Sample Instagram links:")
            for i, link in enumerate(links[:3]):
                try:
                    href = link.get_attribute("href")
                    text = link.text.strip()
                    if text:
                        print(f"         {i+1}. {text} → {href}")
                except:
                    continue
                    
    except Exception as e:
        print(f"      Content extraction error: {e}")

def main():
    """Main debug function"""
    url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    print("🚀 Instagram Container Debug Tool")
    print(f"📱 Target URL: {url}")
    print("=" * 60)
    
    driver = setup_driver()
    
    try:
        # Navigate to the post
        print("📂 Loading Instagram post...")
        driver.get(url)
        time.sleep(8)  # Wait for page to fully load
        
        # Analyze containers
        candidates = analyze_containers(driver)
        
        # Extract sample content from top candidates
        if candidates:
            print(f"\n🔬 Extracting sample content from top 2 candidates:")
            for i, candidate in enumerate(candidates[:2]):
                extract_sample_content(driver, candidate['index'])
                
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        driver.quit()
        print("\n🏁 Debug completed!")

if __name__ == "__main__":
    main()
