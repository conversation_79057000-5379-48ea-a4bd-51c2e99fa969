"""
Configuration settings for Instagram Comment Scraper
"""

# Scraping settings
SCROLL_PAUSE_TIME = 2  # Time to wait between scrolls
MAX_SCROLLS = 10  # Maximum number of scrolls to perform
COMMENT_LOAD_WAIT = 3  # Time to wait for comments to load
IMPLICIT_WAIT = 10  # Selenium implicit wait time

# Output settings
OUTPUT_DIR = "output"
CSV_FILENAME = "instagram_comments.csv"

# Browser settings
HEADLESS = False  # Set to True to run browser in headless mode
WINDOW_SIZE = (1920, 1080)

# Rate limiting
MIN_DELAY = 1  # Minimum delay between actions (seconds)
MAX_DELAY = 3  # Maximum delay between actions (seconds)

# Instagram selectors (these may need updates if Instagram changes their UI)
SELECTORS = {
    'comments_section': 'article',
    'comment_text': 'span',
    'username': 'a',
    'load_more_comments': 'button',
    'view_replies': 'button',
    'comment_container': 'div',
    'comment_list': 'ul'
}

# User agents for rotation
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
]
