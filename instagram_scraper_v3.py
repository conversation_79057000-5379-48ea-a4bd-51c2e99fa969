"""
Instagram Comment Scraper V3
Optimized version with better container detection and scrolling
"""

import os
import csv
import time
import random
import logging
import re
from datetime import datetime
from typing import List, Dict, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper_v3.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class InstagramCommentScraperV3:
    def __init__(self, headless: bool = config.HEADLESS):
        """Initialize the Instagram comment scraper"""
        self.driver = None
        self.wait = None
        self.headless = headless
        self.comments_data = []
        
        # Create output directory
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
            
        # Anti-detection measures
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Set window size
        chrome_options.add_argument(f'--window-size={config.WINDOW_SIZE[0]},{config.WINDOW_SIZE[1]}')
        
        # Random user agent
        user_agent = random.choice(config.USER_AGENTS)
        chrome_options.add_argument(f'--user-agent={user_agent}')
        
        # Initialize driver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Set implicit wait
        self.driver.implicitly_wait(config.IMPLICIT_WAIT)
        self.wait = WebDriverWait(self.driver, config.IMPLICIT_WAIT)
        
        logger.info("WebDriver setup completed")
        
    def random_delay(self):
        """Add random delay to avoid detection"""
        delay = random.uniform(config.MIN_DELAY, config.MAX_DELAY)
        time.sleep(delay)
        
    def find_comment_container(self):
        """Find the scrollable comment container using computed styles"""
        logger.info("Looking for comment container...")
        
        try:
            # Get all div elements
            all_divs = self.driver.find_elements(By.TAG_NAME, "div")
            logger.info(f"Checking {len(all_divs)} div elements for scrollability")
            
            best_container = None
            best_score = 0
            
            for div in all_divs:
                try:
                    # Get computed styles
                    overflow_x = self.driver.execute_script("return window.getComputedStyle(arguments[0]).overflowX", div)
                    overflow_y = self.driver.execute_script("return window.getComputedStyle(arguments[0]).overflowY", div)
                    
                    # Check if scrollable
                    if overflow_x in ['auto', 'scroll'] or overflow_y in ['auto', 'scroll']:
                        scroll_height = self.driver.execute_script("return arguments[0].scrollHeight", div)
                        client_height = self.driver.execute_script("return arguments[0].clientHeight", div)
                        
                        if scroll_height > client_height and client_height > 100:  # Must be reasonably sized
                            # Score based on size and content
                            spans = div.find_elements(By.TAG_NAME, "span")
                            links = div.find_elements(By.TAG_NAME, "a")
                            
                            # Higher score for containers with more text content
                            score = len(spans) + len(links) + (scroll_height - client_height) / 100
                            
                            if score > best_score:
                                best_score = score
                                best_container = div
                                logger.info(f"Found better container: score={score:.1f}, spans={len(spans)}, links={len(links)}")
                                
                except Exception as e:
                    continue
            
            if best_container:
                logger.info(f"Selected comment container with score: {best_score:.1f}")
                return best_container
            else:
                logger.warning("No suitable comment container found")
                return None
                
        except Exception as e:
            logger.error(f"Error finding comment container: {e}")
            return None

    def scroll_comment_container(self, container):
        """Scroll within the comment container to load more comments"""
        logger.info("Scrolling comment container...")
        
        try:
            # First, scroll to top of container
            self.driver.execute_script("arguments[0].scrollTop = 0", container)
            time.sleep(1)
            
            # Get initial metrics
            initial_scroll_height = self.driver.execute_script("return arguments[0].scrollHeight", container)
            logger.info(f"Initial container scroll height: {initial_scroll_height}")
            
            # Scroll down gradually to load content
            for i in range(config.CONTAINER_SCROLL_ATTEMPTS):
                # Get current position
                current_scroll = self.driver.execute_script("return arguments[0].scrollTop", container)
                current_height = self.driver.execute_script("return arguments[0].scrollHeight", container)
                
                # Scroll down by a portion of the container height
                scroll_increment = self.driver.execute_script("return arguments[0].clientHeight * 0.8", container)
                new_scroll_position = current_scroll + scroll_increment
                
                self.driver.execute_script("arguments[0].scrollTop = arguments[1]", container, new_scroll_position)
                time.sleep(1)  # Wait for content to load
                
                # Check if new content loaded
                new_height = self.driver.execute_script("return arguments[0].scrollHeight", container)
                new_scroll = self.driver.execute_script("return arguments[0].scrollTop", container)
                
                logger.info(f"Scroll {i+1}: position={new_scroll:.0f}, height={new_height}")
                
                # If we can't scroll further and no new content, we're done
                if new_scroll == current_scroll and new_height == current_height:
                    logger.info(f"Reached end of container after {i+1} scrolls")
                    break
                    
                # Look for and click "Load more" buttons
                self.click_load_more_buttons()
                
            final_height = self.driver.execute_script("return arguments[0].scrollHeight", container)
            logger.info(f"Final container scroll height: {final_height}")
            
        except Exception as e:
            logger.error(f"Error scrolling container: {e}")

    def click_load_more_buttons(self):
        """Click any 'Load more' or 'View replies' buttons"""
        try:
            button_selectors = [
                "//button[contains(text(), 'Load more')]",
                "//button[contains(text(), 'View replies')]",
                "//span[contains(text(), 'View replies')]",
                "//div[@role='button'][contains(text(), 'replies')]"
            ]
            
            for selector in button_selectors:
                buttons = self.driver.find_elements(By.XPATH, selector)
                for button in buttons:
                    try:
                        if button.is_displayed() and button.is_enabled():
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                            time.sleep(0.5)
                            button.click()
                            logger.info("Clicked load more/view replies button")
                            time.sleep(2)
                            return  # Only click one button per call
                    except:
                        continue
        except:
            pass

    def extract_comments_from_container(self, container) -> List[Dict[str, str]]:
        """Extract comments specifically from the comment container"""
        comments = []
        
        try:
            # Find all links (potential usernames) in the container
            links = container.find_elements(By.TAG_NAME, "a")
            spans = container.find_elements(By.TAG_NAME, "span")
            
            logger.info(f"Found {len(links)} links and {len(spans)} spans in comment container")
            
            # Create a map of potential username links
            username_links = {}
            for link in links:
                try:
                    href = link.get_attribute("href")
                    text = link.text.strip()
                    
                    if (href and text and 
                        'instagram.com/' in href and 
                        len(text) > 0 and len(text) < 30):
                        
                        username = href.split('/')[-1] if href.endswith('/') else href.split('/')[-1]
                        if username and len(username) < 30:
                            username_links[text] = {
                                'username': username,
                                'link': href,
                                'element': link
                            }
                except:
                    continue
            
            logger.info(f"Found {len(username_links)} potential username links")
            
            # Extract comment text from spans
            for span in spans:
                try:
                    text = span.text.strip()
                    if (len(text) > 5 and 
                        not any(skip in text.lower() for skip in 
                               ['follow', 'like', 'share', 'view', 'ago', 'hours', 'minutes'])):
                        
                        # Try to find associated username
                        username = "Unknown"
                        username_link = ""
                        
                        # Look for username in nearby elements
                        parent = span
                        for _ in range(3):  # Check parent levels
                            try:
                                parent = parent.find_element(By.XPATH, "..")
                                parent_links = parent.find_elements(By.TAG_NAME, "a")
                                
                                for link in parent_links:
                                    link_text = link.text.strip()
                                    if link_text in username_links:
                                        username = username_links[link_text]['username']
                                        username_link = username_links[link_text]['link']
                                        break
                                
                                if username != "Unknown":
                                    break
                            except:
                                break
                        
                        comments.append({
                            'username': username,
                            'username_link': username_link,
                            'comment': text,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                        
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"Error extracting from container: {e}")
            
        return comments

    def extract_comments(self) -> List[Dict[str, str]]:
        """Extract comments using optimized container-based approach"""
        logger.info("Extracting comments...")
        
        # Wait for page to load
        time.sleep(config.COMMENT_LOAD_WAIT)
        
        # Find the comment container
        container = self.find_comment_container()
        
        if container:
            # Scroll the container to load more comments
            self.scroll_comment_container(container)
            
            # Extract comments from the container
            comments = self.extract_comments_from_container(container)
        else:
            # Fallback to page-level extraction
            logger.info("Using fallback extraction method...")
            comments = self.extract_from_page_source()
        
        # Remove duplicates
        seen_comments = set()
        unique_comments = []
        
        for comment in comments:
            comment_id = f"{comment['username']}:{comment['comment'][:50]}"
            if comment_id not in seen_comments:
                unique_comments.append(comment)
                seen_comments.add(comment_id)
        
        logger.info(f"Extracted {len(unique_comments)} unique comments")
        return unique_comments

    def extract_from_page_source(self) -> List[Dict[str, str]]:
        """Fallback extraction from page source"""
        comments = []
        page_source = self.driver.page_source
        
        # Extract usernames and comments using regex
        username_pattern = r'href="https://www\.instagram\.com/([^/"]+)/"[^>]*>([^<]+)</a>'
        comment_pattern = r'"text":"([^"]{10,})"'
        
        usernames = re.findall(username_pattern, page_source)
        comment_texts = re.findall(comment_pattern, page_source)
        
        # Simple pairing
        for i, comment_text in enumerate(comment_texts):
            username = usernames[i % len(usernames)][0] if usernames else "Unknown"
            
            comments.append({
                'username': username,
                'username_link': f"https://instagram.com/{username}",
                'comment': comment_text,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            
        return comments
        
    def save_to_csv(self, comments: List[Dict[str, str]], filename: str = None):
        """Save comments to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"instagram_comments_v3_{timestamp}.csv"
            
        filepath = os.path.join(config.OUTPUT_DIR, filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['username', 'username_link', 'comment', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for comment in comments:
                    writer.writerow(comment)
                    
            logger.info(f"Comments saved to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
            
    def scrape_post(self, url: str) -> Optional[str]:
        """Main method to scrape comments from an Instagram post"""
        logger.info(f"Starting to scrape: {url}")
        
        try:
            # Setup driver
            self.setup_driver()
            
            # Navigate to the post
            self.driver.get(url)
            self.random_delay()
            
            # Extract comments
            comments = self.extract_comments()
            
            if comments:
                # Save to CSV
                csv_path = self.save_to_csv(comments)
                logger.info(f"Successfully scraped {len(comments)} comments")
                return csv_path
            else:
                logger.warning("No comments found")
                return None
                
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver closed")


def main():
    """Main function for testing"""
    test_url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    scraper = InstagramCommentScraperV3(headless=False)
    result = scraper.scrape_post(test_url)
    
    if result:
        print(f"✅ Comments successfully scraped and saved to: {result}")
    else:
        print("❌ Failed to scrape comments")


if __name__ == "__main__":
    main()
