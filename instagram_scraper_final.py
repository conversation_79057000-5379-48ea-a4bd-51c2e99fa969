"""
Instagram Comment Scraper - Final Optimized Version
Combines the best approaches for reliable comment extraction
"""

import os
import csv
import time
import random
import logging
import re
from datetime import datetime
from typing import List, Dict, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper_final.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class InstagramCommentScraperFinal:
    def __init__(self, headless: bool = config.HEADLESS):
        """Initialize the Instagram comment scraper"""
        self.driver = None
        self.wait = None
        self.headless = headless
        
        # Create output directory
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
            
        # Anti-detection measures
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Set window size
        chrome_options.add_argument(f'--window-size={config.WINDOW_SIZE[0]},{config.WINDOW_SIZE[1]}')
        
        # Random user agent
        user_agent = random.choice(config.USER_AGENTS)
        chrome_options.add_argument(f'--user-agent={user_agent}')
        
        # Initialize driver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Set implicit wait
        self.driver.implicitly_wait(config.IMPLICIT_WAIT)
        self.wait = WebDriverWait(self.driver, config.IMPLICIT_WAIT)
        
        logger.info("WebDriver setup completed")
        
    def random_delay(self):
        """Add random delay to avoid detection"""
        delay = random.uniform(config.MIN_DELAY, config.MAX_DELAY)
        time.sleep(delay)
        
    def scroll_and_load_content(self):
        """Comprehensive scrolling to load all available content"""
        logger.info("Loading content with multiple scroll approaches...")
        
        # Approach 1: Page scrolling
        logger.info("Phase 1: Page scrolling...")
        for i in range(5):
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
        # Approach 2: Look for and scroll specific containers
        logger.info("Phase 2: Container scrolling...")
        try:
            # Find containers with overflow auto/scroll
            containers = self.driver.execute_script("""
                var containers = [];
                var allDivs = document.querySelectorAll('div');
                for (var i = 0; i < allDivs.length; i++) {
                    var div = allDivs[i];
                    var style = window.getComputedStyle(div);
                    if ((style.overflowY === 'auto' || style.overflowY === 'scroll') && 
                        div.scrollHeight > div.clientHeight && 
                        div.clientHeight > 100) {
                        containers.push(div);
                    }
                }
                return containers;
            """)
            
            logger.info(f"Found {len(containers)} scrollable containers")
            
            # Scroll each container
            for i, container in enumerate(containers):
                try:
                    # Scroll to top first
                    self.driver.execute_script("arguments[0].scrollTop = 0", container)
                    time.sleep(1)
                    
                    # Scroll down gradually
                    for j in range(10):
                        self.driver.execute_script(
                            "arguments[0].scrollTop += arguments[0].clientHeight * 0.8", 
                            container
                        )
                        time.sleep(1)
                        
                    logger.info(f"Scrolled container {i+1}")
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"Container scrolling error: {e}")
            
        # Approach 3: Click load more buttons
        logger.info("Phase 3: Clicking load more buttons...")
        self.click_all_load_more_buttons()
        
    def click_all_load_more_buttons(self):
        """Click all possible load more buttons"""
        button_patterns = [
            "Load more",
            "View more", 
            "View replies",
            "Show more",
            "replies"
        ]
        
        for pattern in button_patterns:
            try:
                # Find buttons by text content
                buttons = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{pattern}')]")
                for button in buttons:
                    try:
                        if button.is_displayed() and button.is_enabled():
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                            time.sleep(0.5)
                            button.click()
                            logger.info(f"Clicked button: {pattern}")
                            time.sleep(2)
                    except:
                        continue
            except:
                continue

    def extract_comments_comprehensive(self) -> List[Dict[str, str]]:
        """Comprehensive comment extraction using multiple methods"""
        logger.info("Starting comprehensive comment extraction...")
        all_comments = []
        
        # Method 1: Direct element extraction
        try:
            comments_method1 = self.extract_by_elements()
            all_comments.extend(comments_method1)
            logger.info(f"Method 1 (elements): Found {len(comments_method1)} comments")
        except Exception as e:
            logger.debug(f"Method 1 failed: {e}")
            
        # Method 2: Page source regex
        try:
            comments_method2 = self.extract_by_page_source()
            all_comments.extend(comments_method2)
            logger.info(f"Method 2 (page source): Found {len(comments_method2)} comments")
        except Exception as e:
            logger.debug(f"Method 2 failed: {e}")
            
        # Method 3: JavaScript extraction
        try:
            comments_method3 = self.extract_by_javascript()
            all_comments.extend(comments_method3)
            logger.info(f"Method 3 (JavaScript): Found {len(comments_method3)} comments")
        except Exception as e:
            logger.debug(f"Method 3 failed: {e}")
        
        # Remove duplicates
        seen = set()
        unique_comments = []
        
        for comment in all_comments:
            key = f"{comment['username']}:{comment['comment'][:50]}"
            if key not in seen:
                seen.add(key)
                unique_comments.append(comment)
        
        logger.info(f"Total unique comments: {len(unique_comments)}")
        return unique_comments

    def extract_by_elements(self) -> List[Dict[str, str]]:
        """Extract comments by finding DOM elements"""
        comments = []
        
        # Find all links that might be usernames
        links = self.driver.find_elements(By.CSS_SELECTOR, "a[href*='instagram.com/']")
        
        for link in links:
            try:
                href = link.get_attribute("href")
                username_text = link.text.strip()
                
                if (href and username_text and 
                    len(username_text) > 0 and len(username_text) < 30 and
                    not any(skip in href for skip in ['/p/', '/reel/', '/tv/', '/explore/'])):
                    
                    # Extract username from URL
                    username = href.split('/')[-1] if href.endswith('/') else href.split('/')[-1]
                    
                    # Look for comment text near this username
                    try:
                        parent = link.find_element(By.XPATH, "../..")
                        spans = parent.find_elements(By.TAG_NAME, "span")
                        
                        for span in spans:
                            text = span.text.strip()
                            if (text and len(text) > 5 and text != username_text and
                                not any(skip in text.lower() for skip in 
                                       ['reply', 'like', 'view', 'follow', 'ago'])):
                                
                                comments.append({
                                    'username': username,
                                    'username_link': href,
                                    'comment': text,
                                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                })
                                break
                    except:
                        continue
                        
            except:
                continue
                
        return comments

    def extract_by_page_source(self) -> List[Dict[str, str]]:
        """Extract comments from page source using regex"""
        comments = []
        page_source = self.driver.page_source
        
        # Find username patterns
        username_pattern = r'href="https://www\.instagram\.com/([^/"]+)/"[^>]*>([^<]+)</a>'
        usernames = re.findall(username_pattern, page_source)
        
        # Find comment text patterns
        text_patterns = [
            r'"text":"([^"]{5,})"',
            r'<span[^>]*dir="auto"[^>]*>([^<]{5,})</span>',
            r'<span[^>]*>([^<]{10,})</span>'
        ]
        
        all_texts = set()
        for pattern in text_patterns:
            matches = re.findall(pattern, page_source)
            for match in matches:
                if not any(skip in match.lower() for skip in 
                          ['follow', 'instagram', 'meta', 'english', 'contact']):
                    all_texts.add(match)
        
        # Pair usernames with texts
        username_list = [u[0] for u in usernames if len(u[0]) < 30]
        text_list = list(all_texts)
        
        for i, text in enumerate(text_list):
            username = username_list[i % len(username_list)] if username_list else "Unknown"
            
            comments.append({
                'username': username,
                'username_link': f"https://instagram.com/{username}",
                'comment': text,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })
            
        return comments

    def extract_by_javascript(self) -> List[Dict[str, str]]:
        """Extract comments using JavaScript to find text content"""
        comments = []
        
        try:
            # Use JavaScript to find all text content
            result = self.driver.execute_script("""
                var comments = [];
                var allSpans = document.querySelectorAll('span');
                var allLinks = document.querySelectorAll('a[href*="instagram.com/"]');
                
                // Create username map
                var usernames = {};
                for (var i = 0; i < allLinks.length; i++) {
                    var link = allLinks[i];
                    var href = link.href;
                    var text = link.textContent.trim();
                    if (text && text.length < 30 && !href.includes('/p/') && !href.includes('/reel/')) {
                        var username = href.split('/').pop();
                        if (username) {
                            usernames[text] = {username: username, link: href};
                        }
                    }
                }
                
                // Find comment texts
                for (var i = 0; i < allSpans.length; i++) {
                    var span = allSpans[i];
                    var text = span.textContent.trim();
                    
                    if (text && text.length > 5 && text.length < 1000) {
                        // Skip UI elements
                        if (text.toLowerCase().includes('follow') || 
                            text.toLowerCase().includes('like') ||
                            text.toLowerCase().includes('view') ||
                            text.toLowerCase().includes('ago')) {
                            continue;
                        }
                        
                        comments.push({
                            text: text,
                            username: 'Unknown'
                        });
                    }
                }
                
                return {comments: comments, usernames: usernames};
            """)
            
            js_comments = result.get('comments', [])
            js_usernames = result.get('usernames', {})
            
            # Convert to our format
            username_keys = list(js_usernames.keys())
            
            for i, comment_data in enumerate(js_comments):
                username_key = username_keys[i % len(username_keys)] if username_keys else None
                
                if username_key and username_key in js_usernames:
                    username = js_usernames[username_key]['username']
                    username_link = js_usernames[username_key]['link']
                else:
                    username = "Unknown"
                    username_link = ""
                
                comments.append({
                    'username': username,
                    'username_link': username_link,
                    'comment': comment_data['text'],
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                
        except Exception as e:
            logger.debug(f"JavaScript extraction error: {e}")
            
        return comments
        
    def save_to_csv(self, comments: List[Dict[str, str]], filename: str = None):
        """Save comments to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"instagram_comments_final_{timestamp}.csv"
            
        filepath = os.path.join(config.OUTPUT_DIR, filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['username', 'username_link', 'comment', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for comment in comments:
                    writer.writerow(comment)
                    
            logger.info(f"Comments saved to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
            
    def scrape_post(self, url: str) -> Optional[str]:
        """Main method to scrape comments from an Instagram post"""
        logger.info(f"Starting to scrape: {url}")
        
        try:
            # Setup driver
            self.setup_driver()
            
            # Navigate to the post
            self.driver.get(url)
            time.sleep(5)  # Wait for initial load
            
            # Load all content
            self.scroll_and_load_content()
            
            # Extract comments
            comments = self.extract_comments_comprehensive()
            
            if comments:
                # Save to CSV
                csv_path = self.save_to_csv(comments)
                logger.info(f"Successfully scraped {len(comments)} comments")
                return csv_path
            else:
                logger.warning("No comments found")
                return None
                
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver closed")


def main():
    """Main function for testing"""
    test_url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    scraper = InstagramCommentScraperFinal(headless=False)
    result = scraper.scrape_post(test_url)
    
    if result:
        print(f"✅ Comments successfully scraped and saved to: {result}")
    else:
        print("❌ Failed to scrape comments")


if __name__ == "__main__":
    main()
