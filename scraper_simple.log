2025-06-06 16:18:40,876 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN/
2025-06-06 16:18:40,876 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:18:40,876 - INFO - ====== WebDriver manager ======
2025-06-06 16:18:40,957 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:18:41,026 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:18:41,091 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:18:41,475 - INFO - ✅ Connected to existing Chrome session!
2025-06-06 16:18:47,932 - INFO - Looking for scrollable comment container...
2025-06-06 16:18:47,947 - INFO - Found comment container: 205 spans
2025-06-06 16:18:47,947 - INFO - Container size: 1619 scroll / 378 client
2025-06-06 16:18:47,948 - INFO - Scrolling comment container to load all comments...
2025-06-06 16:18:49,967 - INFO - Initial container scroll height: 1619
2025-06-06 16:18:52,007 - INFO - Scroll 1: position=302, height=1619
2025-06-06 16:19:04,102 - INFO - Scroll 2: position=605, height=1619
2025-06-06 16:19:16,160 - INFO - Scroll 3: position=908, height=1619
2025-06-06 16:19:28,225 - INFO - Scroll 4: position=1210, height=2354
2025-06-06 16:19:40,313 - INFO - Scroll 5: position=1512, height=2354
2025-06-06 16:19:52,346 - INFO - Scroll 6: position=1815, height=2354
2025-06-06 16:20:04,417 - INFO - Scroll 7: position=1976, height=2522
2025-06-06 16:20:16,456 - INFO - Scroll 8: position=2078, height=2456
2025-06-06 16:20:28,516 - INFO - Scroll 9: position=2078, height=2456
2025-06-06 16:20:38,522 - INFO - Reached end of container after 9 scrolls
2025-06-06 16:20:38,527 - INFO - Final container scroll height: 2456 (loaded 837 more pixels)
2025-06-06 16:20:38,527 - INFO - Extracting username-comment pairs from container...
2025-06-06 16:20:38,536 - INFO - Found 2 username-comment pairs
2025-06-06 16:20:38,536 - INFO - Extracted 2 comments from container
2025-06-06 16:20:38,538 - INFO - Comments saved to output/instagram_comments_simple_20250606_162038.csv
2025-06-06 16:20:38,538 - INFO - Successfully scraped 1 unique comments
2025-06-06 16:20:38,562 - INFO - WebDriver closed
2025-06-06 16:21:25,738 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN/
2025-06-06 16:21:25,738 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:21:25,738 - INFO - ====== WebDriver manager ======
2025-06-06 16:21:25,825 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:21:25,896 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:21:25,951 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:21:26,334 - INFO - ✅ Connected to existing Chrome session!
2025-06-06 16:21:33,157 - INFO - Looking for scrollable comment container...
2025-06-06 16:21:33,170 - INFO - Found comment container: 205 spans
2025-06-06 16:21:33,170 - INFO - Container size: 1619 scroll / 378 client
2025-06-06 16:21:33,170 - INFO - Scrolling comment container to load all comments...
2025-06-06 16:21:35,190 - INFO - Initial container scroll height: 1619
2025-06-06 16:21:37,232 - INFO - Scroll 1: position=302, height=1619
2025-06-06 16:21:49,307 - INFO - Scroll 2: position=605, height=1619
2025-06-06 16:22:01,380 - INFO - Scroll 3: position=908, height=1619
2025-06-06 16:22:13,459 - INFO - Scroll 4: position=1210, height=2438
2025-06-06 16:22:25,548 - INFO - Scroll 5: position=1512, height=2438
2025-06-06 16:22:37,647 - INFO - Scroll 6: position=1815, height=2438
2025-06-06 16:22:49,706 - INFO - Scroll 7: position=2060, height=2456
2025-06-06 16:23:01,752 - INFO - Scroll 8: position=2078, height=2456
2025-06-06 16:23:13,842 - INFO - Scroll 9: position=2078, height=2456
2025-06-06 16:23:23,896 - INFO - Reached end of container after 9 scrolls
2025-06-06 16:23:23,900 - INFO - Final container scroll height: 2456 (loaded 837 more pixels)
2025-06-06 16:23:23,901 - INFO - Extracting username-comment pairs from container...
2025-06-06 16:23:23,910 - INFO - Found 63 username-comment pairs
2025-06-06 16:23:23,910 - INFO - Extracted 63 comments from container
2025-06-06 16:23:23,912 - INFO - Comments saved to output/instagram_comments_simple_20250606_162323.csv
2025-06-06 16:23:23,912 - INFO - Successfully scraped 45 unique comments
2025-06-06 16:23:23,931 - INFO - WebDriver closed
2025-06-06 16:23:57,349 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN/
2025-06-06 16:23:57,349 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:23:57,349 - INFO - ====== WebDriver manager ======
2025-06-06 16:23:57,437 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:23:57,509 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:23:57,575 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:23:57,953 - INFO - ✅ Connected to existing Chrome session!
2025-06-06 16:24:06,911 - INFO - Looking for scrollable comment container...
2025-06-06 16:24:06,924 - INFO - Found comment container: 205 spans
2025-06-06 16:24:06,925 - INFO - Container size: 1601 scroll / 378 client
2025-06-06 16:24:06,925 - INFO - Scrolling comment container to load all comments...
2025-06-06 16:24:08,945 - INFO - Initial container scroll height: 1601
2025-06-06 16:24:10,982 - INFO - Scroll 1: position=302, height=1601
2025-06-06 16:24:23,034 - INFO - Scroll 2: position=605, height=1601
2025-06-06 16:24:35,115 - INFO - Scroll 3: position=908, height=1601
2025-06-06 16:24:47,164 - INFO - Scroll 4: position=1210, height=2522
2025-06-06 16:24:59,216 - INFO - Scroll 5: position=1512, height=2522
2025-06-06 16:25:11,304 - INFO - Scroll 6: position=1815, height=2522
2025-06-06 16:25:23,399 - INFO - Scroll 7: position=2078, height=2456
2025-06-06 16:25:35,461 - INFO - Scroll 8: position=2078, height=2456
2025-06-06 16:25:45,512 - INFO - Reached end of container after 8 scrolls
2025-06-06 16:25:45,518 - INFO - Final container scroll height: 2456 (loaded 855 more pixels)
2025-06-06 16:25:45,518 - INFO - Extracting username-comment pairs from container...
2025-06-06 16:25:45,527 - INFO - Found 23 username-comment pairs
2025-06-06 16:25:45,527 - INFO - Extracted 23 comments from container
2025-06-06 16:25:45,528 - INFO - Comments saved to output/instagram_comments_simple_20250606_162545.csv
2025-06-06 16:25:45,528 - INFO - Successfully scraped 23 unique comments
2025-06-06 16:25:45,547 - INFO - WebDriver closed
