# Instagram Comment Scraper

A Python-based tool to scrape comments and usernames from Instagram posts/reels and export them to CSV format.

## Features

- 🔍 Scrapes comments from Instagram posts and reels
- 👤 Extracts usernames and comment text
- 📊 Exports data to CSV format
- 🤖 Anti-detection measures to avoid blocking
- 📜 <PERSON>les infinite scroll to load more comments
- ⚙️ Configurable settings
- 🧪 Comprehensive testing suite

## Quick Start

### Option 1: Automated Setup
```bash
python setup.py
```

### Option 2: Manual Setup

1. **Create virtual environment:**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the scraper:**
   ```bash
   python run_scraper.py
   ```

## Usage

### Simple Command Line Usage

```bash
# Scrape the default test URL
python run_scraper.py

# Scrape a specific Instagram URL
python run_scraper.py "https://www.instagram.com/reel/YOUR_REEL_ID/"
```

### Python Code Usage

```python
from instagram_scraper import InstagramCommentScraper

# Initialize scraper
scraper = InstagramCommentScraper(headless=False)  # Set to True for headless mode

# Scrape comments from a post/reel
url = "https://www.instagram.com/reel/DKcj0VdORgN/"
csv_file = scraper.scrape_post(url)

if csv_file:
    print(f"Comments saved to: {csv_file}")
```

### Testing

Run unit tests:
```bash
python test_scraper.py
```

Run manual integration test:
```bash
python test_scraper.py manual
```

## Configuration

Edit `config.py` to customize scraping behavior:

- `SCROLL_PAUSE_TIME`: Time between scrolls (seconds)
- `MAX_SCROLLS`: Maximum number of scrolls to perform
- `HEADLESS`: Run browser in headless mode
- `OUTPUT_DIR`: Directory to save CSV files
- Rate limiting and delay settings

## Output Format

The scraper exports data to CSV with the following columns:

| Column | Description |
|--------|-------------|
| username | Instagram username of commenter |
| comment | Full comment text |
| timestamp | When the comment was scraped |

## Important Notes

### Legal and Ethical Considerations

- ⚖️ **Respect Instagram's Terms of Service**
- 🤝 **Use responsibly and ethically**
- 📊 **Only scrape public content**
- 🚫 **Don't overload Instagram's servers**
- 🔒 **Respect user privacy**

### Technical Limitations

- Instagram frequently updates their UI, which may break selectors
- Rate limiting may be applied by Instagram
- Some comments may not be accessible without login
- Large posts with many comments may take time to scrape

### Troubleshooting

**Common Issues:**

1. **No comments found:**
   - Instagram may have changed their HTML structure
   - The post may require login to view comments
   - Try updating selectors in `config.py`

2. **WebDriver errors:**
   - Ensure Chrome browser is installed
   - Check internet connection
   - Try running in non-headless mode for debugging

3. **Access blocked:**
   - Instagram may have detected automated behavior
   - Try increasing delays in config
   - Use different user agents
   - Take breaks between scraping sessions

## File Structure

```
├── instagram_scraper.py    # Main scraper class
├── config.py              # Configuration settings
├── test_scraper.py        # Test suite
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── output/               # Directory for CSV exports
```

## Example Output

```csv
username,comment,timestamp
john_doe,Great post! Love this content,2024-01-15 14:30:22
jane_smith,Amazing work 👏,2024-01-15 14:30:22
user123,This is so inspiring!,2024-01-15 14:30:22
```

## Contributing

Feel free to submit issues and enhancement requests!

## Disclaimer

This tool is for educational and research purposes only. Users are responsible for complying with Instagram's Terms of Service and applicable laws. The authors are not responsible for any misuse of this tool.
