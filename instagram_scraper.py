"""
Instagram Comment Scraper
Scrapes comments and usernames from Instagram posts/reels and exports to CSV
"""

import os
import csv
import time
import random
import logging
import re
from datetime import datetime
from typing import List, Dict, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class InstagramCommentScraper:
    def __init__(self, headless: bool = config.HEADLESS):
        """Initialize the Instagram comment scraper"""
        self.driver = None
        self.wait = None
        self.headless = headless
        self.comments_data = []
        
        # Create output directory
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
            
        # Anti-detection measures
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Set window size
        chrome_options.add_argument(f'--window-size={config.WINDOW_SIZE[0]},{config.WINDOW_SIZE[1]}')
        
        # Random user agent
        user_agent = random.choice(config.USER_AGENTS)
        chrome_options.add_argument(f'--user-agent={user_agent}')
        
        # Initialize driver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Set implicit wait
        self.driver.implicitly_wait(config.IMPLICIT_WAIT)
        self.wait = WebDriverWait(self.driver, config.IMPLICIT_WAIT)
        
        logger.info("WebDriver setup completed")
        
    def random_delay(self):
        """Add random delay to avoid detection"""
        delay = random.uniform(config.MIN_DELAY, config.MAX_DELAY)
        time.sleep(delay)
        
    def scroll_to_load_comments(self):
        """Scroll down to load more comments"""
        logger.info("Scrolling to load comments...")
        
        for i in range(config.MAX_SCROLLS):
            # Scroll down
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            self.random_delay()
            
            # Try to click "Load more comments" button if it exists
            try:
                # Look for various "Load more" button patterns
                load_more_selectors = [
                    'button[aria-label*="Load more"]',
                    'button[aria-label*="View more"]',
                    'button:contains("Load more")',
                    'button:contains("View more")',
                    'div[role="button"]:contains("Load more")'
                ]

                for selector in load_more_selectors:
                    try:
                        load_more_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if load_more_btn.is_displayed():
                            load_more_btn.click()
                            logger.info(f"Clicked 'Load more comments' button (scroll {i+1})")
                            time.sleep(config.COMMENT_LOAD_WAIT)
                            break
                    except:
                        continue
            except Exception:
                pass
                
            # Check if we've reached the end
            current_height = self.driver.execute_script("return document.body.scrollHeight")
            if i > 0 and current_height == getattr(self, 'last_height', 0):
                logger.info("Reached end of comments")
                break
            self.last_height = current_height
            
        logger.info(f"Completed {i+1} scrolls")
        


    def extract_comments(self) -> List[Dict[str, str]]:
        """Extract comments using container-based approach"""
        logger.info("Extracting comments...")

        # Wait for page to load
        time.sleep(config.COMMENT_LOAD_WAIT)

        # Find the comment container
        container = self.find_comment_container()

        if container:
            # Scroll the container to load more comments
            self.scroll_comment_container(container)

            # Extract comments from the container
            comments = self.extract_username_comment_pairs(container)
        else:
            logger.warning("No comment container found")
            comments = []

        # Remove duplicates
        seen = set()
        unique_comments = []

        for comment in comments:
            key = f"{comment['username']}:{comment['comment'][:50]}"
            if key not in seen:
                seen.add(key)
                unique_comments.append(comment)

        logger.info(f"Extracted {len(unique_comments)} unique comments")
        return unique_comments
        
    def save_to_csv(self, comments: List[Dict[str, str]], filename: str = None):
        """Save comments to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"instagram_comments_{timestamp}.csv"

        filepath = os.path.join(config.OUTPUT_DIR, filename)

        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['username', 'username_link', 'comment', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for comment in comments:
                    # Ensure all required fields are present
                    row = {
                        'username': comment.get('username', 'Unknown'),
                        'username_link': comment.get('username_link', ''),
                        'comment': comment.get('comment', ''),
                        'timestamp': comment.get('timestamp', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                    }
                    writer.writerow(row)

            logger.info(f"Comments saved to {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
            
    def scrape_post(self, url: str) -> Optional[str]:
        """Main method to scrape comments from an Instagram post"""
        logger.info(f"Starting to scrape: {url}")
        
        try:
            # Setup driver
            self.setup_driver()
            
            # Navigate to the post
            self.driver.get(url)
            self.random_delay()
            
            # Scroll to load comments
            self.scroll_to_load_comments()
            
            # Extract comments
            comments = self.extract_comments()
            
            if comments:
                # Save to CSV
                csv_path = self.save_to_csv(comments)
                logger.info(f"Successfully scraped {len(comments)} comments")
                return csv_path
            else:
                logger.warning("No comments found")
                return None
                
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver closed")


def main():
    """Main function for testing"""
    # Test URL
    test_url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    scraper = InstagramCommentScraper(headless=False, use_existing_chrome=True)
    result = scraper.scrape_post(test_url)
    
    if result:
        print(f"✅ Comments successfully scraped and saved to: {result}")
    else:
        print("❌ Failed to scrape comments")


if __name__ == "__main__":
    main()
