"""
Instagram Comment Scraper
Scrapes comments and usernames from Instagram posts/reels and exports to CSV
"""

import os
import csv
import time
import random
import logging

from datetime import datetime
from typing import List, Dict, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class InstagramCommentScraper:
    def __init__(self, headless: bool = config.HEADLESS, use_existing_chrome: bool = True):
        """Initialize the Instagram comment scraper"""
        self.driver = None
        self.wait = None
        self.headless = headless
        self.use_existing_chrome = use_existing_chrome

        # Create output directory
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
        
    def setup_driver(self):
        """Setup Chrome WebDriver - opens new tab in existing Chrome or creates new session"""
        logger.info("Setting up Chrome WebDriver...")

        # Always try to use existing Chrome first
        try:
            # Try to connect to existing Chrome session
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")

            # Initialize driver to connect to existing session
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            logger.info("✅ Connected to existing Chrome session - will open new tab")

        except Exception as e:
            logger.info("No existing Chrome session found, creating new one...")

            # Create new Chrome session
            chrome_options = Options()

            if self.headless:
                chrome_options.add_argument('--headless')

            # Anti-detection measures
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Set window size
            chrome_options.add_argument(f'--window-size={config.WINDOW_SIZE[0]},{config.WINDOW_SIZE[1]}')

            # Random user agent
            user_agent = random.choice(config.USER_AGENTS)
            chrome_options.add_argument(f'--user-agent={user_agent}')

            # Initialize driver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Execute script to remove webdriver property
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("✅ Created new Chrome session")

        # Set implicit wait
        self.driver.implicitly_wait(config.IMPLICIT_WAIT)
        self.wait = WebDriverWait(self.driver, config.IMPLICIT_WAIT)
        
    def random_delay(self):
        """Add random delay to avoid detection"""
        delay = random.uniform(config.MIN_DELAY, config.MAX_DELAY)
        time.sleep(delay)
        


    def find_comment_container(self):
        """Find the scrollable comment container"""
        logger.info("Looking for scrollable comment container...")

        try:
            # Use JavaScript to find containers with overflow auto/scroll
            containers = self.driver.execute_script("""
                var containers = [];
                var allDivs = document.querySelectorAll('div');

                for (var i = 0; i < allDivs.length; i++) {
                    var div = allDivs[i];
                    var style = window.getComputedStyle(div);

                    // Check if it's scrollable
                    if ((style.overflowY === 'auto' || style.overflowY === 'scroll') &&
                        div.scrollHeight > div.clientHeight &&
                        div.clientHeight > 200) {  // Must be reasonably sized

                        // Check if it contains potential comment content (lots of spans)
                        var spans = div.querySelectorAll('span');

                        // Lower threshold - just need lots of text content
                        if (spans.length > 50) {  // Lots of text content suggests comments
                            containers.push({
                                element: div,
                                scrollHeight: div.scrollHeight,
                                clientHeight: div.clientHeight,
                                spanCount: spans.length,
                                score: spans.length  // Score based on text content
                            });
                        }
                    }
                }

                // Sort by score (most content)
                containers.sort(function(a, b) { return b.score - a.score; });
                return containers;
            """)

            if containers:
                best_container = containers[0]
                logger.info(f"Found comment container: {best_container['spanCount']} spans")
                logger.info(f"Container size: {best_container['scrollHeight']} scroll / {best_container['clientHeight']} client")
                return best_container['element']
            else:
                logger.warning("No suitable comment container found")
                return None

        except Exception as e:
            logger.error(f"Error finding comment container: {e}")
            return None

    def scroll_comment_container(self, container):
        """Scroll the comment container to load all comments"""
        logger.info("Scrolling comment container to load all comments...")

        try:
            # First scroll to top
            self.driver.execute_script("arguments[0].scrollTop = 0", container)
            time.sleep(2)

            # Get initial state
            initial_height = self.driver.execute_script("return arguments[0].scrollHeight", container)
            logger.info(f"Initial container scroll height: {initial_height}")

            # Scroll down gradually
            for i in range(config.CONTAINER_SCROLL_ATTEMPTS):
                # Get current scroll position
                current_scroll = self.driver.execute_script("return arguments[0].scrollTop", container)
                current_height = self.driver.execute_script("return arguments[0].scrollHeight", container)

                # Scroll down by container height
                client_height = self.driver.execute_script("return arguments[0].clientHeight", container)
                new_scroll = current_scroll + (client_height * 0.8)

                self.driver.execute_script("arguments[0].scrollTop = arguments[1]", container, new_scroll)
                time.sleep(2)  # Wait for content to load

                # Check if we've reached the bottom or no new content
                new_scroll_pos = self.driver.execute_script("return arguments[0].scrollTop", container)
                new_height = self.driver.execute_script("return arguments[0].scrollHeight", container)

                logger.info(f"Scroll {i+1}: position={new_scroll_pos:.0f}, height={new_height}")

                # Try to click "View replies" buttons
                self.click_view_replies_in_container(container)

                # If we can't scroll further, we're done
                if new_scroll_pos == current_scroll and new_height == current_height:
                    logger.info(f"Reached end of container after {i+1} scrolls")
                    break

            final_height = self.driver.execute_script("return arguments[0].scrollHeight", container)
            logger.info(f"Final container scroll height: {final_height} (loaded {final_height - initial_height} more pixels)")

        except Exception as e:
            logger.error(f"Error scrolling container: {e}")

    def click_view_replies_in_container(self, container):
        """Click 'View replies' buttons within the container"""
        try:
            # Find reply buttons within the container
            reply_buttons = container.find_elements(By.XPATH, ".//button[contains(text(), 'View replies')] | .//span[contains(text(), 'View replies')]")

            for button in reply_buttons:
                try:
                    if button.is_displayed() and button.is_enabled():
                        # Scroll button into view within container
                        self.driver.execute_script("arguments[1].scrollTop = arguments[0].offsetTop - arguments[1].clientHeight/2", button, container)
                        time.sleep(0.5)
                        button.click()
                        logger.info("Clicked 'View replies' button")
                        time.sleep(1)
                except:
                    continue
        except:
            pass

    def extract_username_comment_pairs(self, container) -> List[Dict[str, str]]:
        """Extract username-comment pairs from the container using HTML structure"""
        logger.info("Extracting username-comment pairs from container...")
        comments = []

        try:
            # Use JavaScript to extract username-comment pairs from the DOM
            pairs = self.driver.execute_script("""
                var pairs = [];
                var container = arguments[0];

                // Find all spans that might contain usernames or comments
                var allSpans = container.querySelectorAll('span');

                for (var i = 0; i < allSpans.length; i++) {
                    var span = allSpans[i];
                    var text = span.textContent.trim();

                    // Skip empty or very short text
                    if (!text || text.length < 3) continue;

                    // More flexible username detection
                    var isUsername = false;

                    // Check if this looks like a username
                    if (text.length <= 35 && !text.includes(' ') && text.length > 2) {
                        // Allow letters, numbers, dots, underscores, and some special chars
                        if (/^[a-zA-Z0-9._-]+$/.test(text.replace('Verified', ''))) {
                            isUsername = true;
                        }
                        // Also check for common username patterns
                        if (/^[a-zA-Z][a-zA-Z0-9._]{1,29}$/.test(text.replace('Verified', ''))) {
                            isUsername = true;
                        }
                    }

                    if (isUsername) {
                        // Clean username (remove 'Verified' suffix)
                        var cleanUsername = text.replace('Verified', '').replace(/[^a-zA-Z0-9._]/g, '');

                        // Look for comment text in the same container or nearby
                        var commentText = '';

                        // Strategy 1: Look in the same parent for other spans
                        var parent = span.parentElement;
                        if (parent) {
                            var siblingSpans = parent.querySelectorAll('span');
                            for (var j = 0; j < siblingSpans.length; j++) {
                                var siblingSpan = siblingSpans[j];
                                var siblingText = siblingSpan.textContent.trim();

                                if (siblingText &&
                                    siblingText.length > 5 &&
                                    siblingText !== text &&
                                    siblingText !== cleanUsername &&
                                    !siblingText.match(/^[0-9]+[smhd]$/) && // Skip time indicators
                                    !siblingText.toLowerCase().includes('reply') &&
                                    !siblingText.toLowerCase().includes('like') &&
                                    !siblingText.toLowerCase().includes('view') &&
                                    !siblingText.toLowerCase().includes('follow') &&
                                    siblingText.includes(' ')) { // Comments usually have spaces
                                    commentText = siblingText;
                                    break;
                                }
                            }
                        }

                        // Strategy 2: Look in grandparent if no comment found
                        if (!commentText && parent && parent.parentElement) {
                            var grandParent = parent.parentElement;
                            var grandSpans = grandParent.querySelectorAll('span');
                            for (var k = 0; k < grandSpans.length; k++) {
                                var grandSpan = grandSpans[k];
                                var grandText = grandSpan.textContent.trim();

                                if (grandText &&
                                    grandText.length > 10 &&
                                    grandText !== text &&
                                    grandText !== cleanUsername &&
                                    !grandText.match(/^[0-9]+[smhd]$/) &&
                                    !grandText.toLowerCase().includes('reply') &&
                                    !grandText.toLowerCase().includes('like') &&
                                    !grandText.toLowerCase().includes('view') &&
                                    !grandText.toLowerCase().includes('follow') &&
                                    grandText.includes(' ')) {
                                    commentText = grandText;
                                    break;
                                }
                            }
                        }

                        // Filter out UI elements that aren't real usernames
                        var isUIElement = ['Reply', 'Like', 'View', 'Follow', 'Share', 'Save', 'More', 'See'].includes(cleanUsername);

                        if (commentText && cleanUsername && !isUIElement) {
                            pairs.push({
                                username: cleanUsername,
                                comment: commentText
                            });
                        }
                    }
                }

                return pairs;
            """, container)

            logger.info(f"Found {len(pairs)} username-comment pairs")

            # Convert to our format and construct Instagram URLs
            for pair in pairs:
                username = pair['username']
                comment_text = pair['comment']

                # Skip if username is too short or comment is too short
                if len(username) < 3 or len(comment_text) < 5:
                    continue

                # Construct Instagram URL
                instagram_url = f"https://instagram.com/{username}"

                comments.append({
                    'username': username,
                    'username_link': instagram_url,
                    'comment': comment_text,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

        except Exception as e:
            logger.error(f"Error extracting username-comment pairs: {e}")

        logger.info(f"Extracted {len(comments)} comments from container")
        return comments

    def extract_comments(self) -> List[Dict[str, str]]:
        """Extract comments using container-based approach"""
        logger.info("Extracting comments...")

        # Wait for page to load
        time.sleep(config.COMMENT_LOAD_WAIT)

        # Find the comment container
        container = self.find_comment_container()

        if container:
            # Scroll the container to load more comments
            self.scroll_comment_container(container)

            # Extract comments from the container
            comments = self.extract_username_comment_pairs(container)
        else:
            logger.warning("No comment container found")
            comments = []

        # Remove duplicates
        seen = set()
        unique_comments = []

        for comment in comments:
            key = f"{comment['username']}:{comment['comment'][:50]}"
            if key not in seen:
                seen.add(key)
                unique_comments.append(comment)

        logger.info(f"Extracted {len(unique_comments)} unique comments")
        return unique_comments
        
    def save_to_csv(self, comments: List[Dict[str, str]], filename: str = None):
        """Save comments to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"instagram_comments_{timestamp}.csv"

        filepath = os.path.join(config.OUTPUT_DIR, filename)

        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['username', 'username_link', 'comment', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for comment in comments:
                    # Ensure all required fields are present
                    row = {
                        'username': comment.get('username', 'Unknown'),
                        'username_link': comment.get('username_link', ''),
                        'comment': comment.get('comment', ''),
                        'timestamp': comment.get('timestamp', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                    }
                    writer.writerow(row)

            logger.info(f"Comments saved to {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
            
    def scrape_post(self, url: str) -> Optional[str]:
        """Main method to scrape comments from an Instagram post"""
        logger.info(f"Starting to scrape: {url}")
        
        try:
            # Setup driver
            self.setup_driver()
            
            # Navigate to the post (opens in new tab if using existing Chrome)
            logger.info(f"Opening Instagram post: {url}")

            # If we're using existing Chrome, open in a new tab
            try:
                # Try to open in new tab
                self.driver.execute_script("window.open('');")
                self.driver.switch_to.window(self.driver.window_handles[-1])
                logger.info("Opened new tab in existing Chrome session")
            except:
                # If that fails, just use current tab
                logger.info("Using current tab")

            self.driver.get(url)
            time.sleep(5)  # Wait for initial load

            # Extract comments (this will handle container detection and scrolling)
            comments = self.extract_comments()
            
            if comments:
                # Save to CSV
                csv_path = self.save_to_csv(comments)
                logger.info(f"Successfully scraped {len(comments)} comments")
                return csv_path
            else:
                logger.warning("No comments found")
                return None
                
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver closed")


def main():
    """Main function for testing"""
    # Test URL
    test_url = "https://www.instagram.com/p/DJ9_dZEu7KM/"

    scraper = InstagramCommentScraper(headless=False, use_existing_chrome=True)
    result = scraper.scrape_post(test_url)

    if result:
        print(f"✅ Comments successfully scraped and saved to: {result}")
    else:
        print("❌ Failed to scrape comments")


if __name__ == "__main__":
    main()
