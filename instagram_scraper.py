"""
Instagram Comment Scraper
Scrapes comments and usernames from Instagram posts/reels and exports to CSV
"""

import os
import csv
import time
import random
import logging
from datetime import datetime
from typing import List, Dict, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class InstagramCommentScraper:
    def __init__(self, headless: bool = config.HEADLESS):
        """Initialize the Instagram comment scraper"""
        self.driver = None
        self.wait = None
        self.headless = headless
        self.comments_data = []
        
        # Create output directory
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
            
        # Anti-detection measures
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Set window size
        chrome_options.add_argument(f'--window-size={config.WINDOW_SIZE[0]},{config.WINDOW_SIZE[1]}')
        
        # Random user agent
        user_agent = random.choice(config.USER_AGENTS)
        chrome_options.add_argument(f'--user-agent={user_agent}')
        
        # Initialize driver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Set implicit wait
        self.driver.implicitly_wait(config.IMPLICIT_WAIT)
        self.wait = WebDriverWait(self.driver, config.IMPLICIT_WAIT)
        
        logger.info("WebDriver setup completed")
        
    def random_delay(self):
        """Add random delay to avoid detection"""
        delay = random.uniform(config.MIN_DELAY, config.MAX_DELAY)
        time.sleep(delay)
        
    def scroll_to_load_comments(self):
        """Scroll down to load more comments"""
        logger.info("Scrolling to load comments...")
        
        for i in range(config.MAX_SCROLLS):
            # Scroll down
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            self.random_delay()
            
            # Try to click "Load more comments" button if it exists
            try:
                # Look for various "Load more" button patterns
                load_more_selectors = [
                    'button[aria-label*="Load more"]',
                    'button[aria-label*="View more"]',
                    'button:contains("Load more")',
                    'button:contains("View more")',
                    'div[role="button"]:contains("Load more")'
                ]

                for selector in load_more_selectors:
                    try:
                        load_more_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if load_more_btn.is_displayed():
                            load_more_btn.click()
                            logger.info(f"Clicked 'Load more comments' button (scroll {i+1})")
                            time.sleep(config.COMMENT_LOAD_WAIT)
                            break
                    except:
                        continue
            except Exception:
                pass
                
            # Check if we've reached the end
            current_height = self.driver.execute_script("return document.body.scrollHeight")
            if i > 0 and current_height == getattr(self, 'last_height', 0):
                logger.info("Reached end of comments")
                break
            self.last_height = current_height
            
        logger.info(f"Completed {i+1} scrolls")
        
    def extract_comments(self) -> List[Dict[str, str]]:
        """Extract comments and usernames from the page"""
        logger.info("Extracting comments...")
        comments = []

        try:
            # Wait for page to load
            time.sleep(config.COMMENT_LOAD_WAIT)

            # Get page source and parse with BeautifulSoup as fallback
            page_source = self.driver.page_source

            # Try to find comment patterns in the page source
            # Look for text patterns that might be comments
            import re

            # Pattern to find potential comments (text between quotes or in specific structures)
            comment_patterns = [
                r'"text":"([^"]+)"',  # JSON-like structure
                r'<span[^>]*>([^<]+)</span>',  # Span elements
                r'"caption":"([^"]+)"',  # Caption field
            ]

            found_texts = set()
            for pattern in comment_patterns:
                matches = re.findall(pattern, page_source)
                for match in matches:
                    if len(match.strip()) > 10 and not match.startswith('http'):  # Filter out URLs and short text
                        found_texts.add(match.strip())

            # Also try Selenium approach with simpler selectors
            try:
                # Find all span elements that might contain comments
                span_elements = self.driver.find_elements(By.TAG_NAME, "span")
                logger.info(f"Found {len(span_elements)} span elements")

                for span in span_elements:
                    try:
                        text = span.text.strip()
                        if len(text) > 10 and not text.startswith('http') and text not in found_texts:
                            found_texts.add(text)
                    except:
                        continue

            except Exception as e:
                logger.debug(f"Error with Selenium approach: {e}")

            # Convert found texts to comment format
            processed_comments = set()

            for text in found_texts:
                try:
                    # Skip if text looks like UI elements
                    if any(skip_word in text.lower() for skip_word in ['follow', 'like', 'share', 'comment', 'view', 'ago', 'hours', 'minutes']):
                        continue

                    # Try to extract username if text contains @ or looks like "username: comment"
                    username = "Unknown"
                    comment_text = text

                    # Check if text has username pattern
                    if ':' in text and len(text.split(':')[0]) < 30:
                        parts = text.split(':', 1)
                        potential_username = parts[0].strip()
                        if len(potential_username) > 0 and len(potential_username) < 30:
                            username = potential_username
                            comment_text = parts[1].strip()

                    # Create unique identifier
                    comment_id = f"{username}:{comment_text[:50]}"

                    if comment_id not in processed_comments and len(comment_text) > 5:
                        comments.append({
                            'username': username,
                            'comment': comment_text,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                        processed_comments.add(comment_id)

                except Exception as e:
                    logger.debug(f"Error processing text: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error extracting comments: {e}")

        logger.info(f"Extracted {len(comments)} potential comments/text")
        return comments
        
    def save_to_csv(self, comments: List[Dict[str, str]], filename: str = None):
        """Save comments to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"instagram_comments_{timestamp}.csv"
            
        filepath = os.path.join(config.OUTPUT_DIR, filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['username', 'comment', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for comment in comments:
                    writer.writerow(comment)
                    
            logger.info(f"Comments saved to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
            
    def scrape_post(self, url: str) -> Optional[str]:
        """Main method to scrape comments from an Instagram post"""
        logger.info(f"Starting to scrape: {url}")
        
        try:
            # Setup driver
            self.setup_driver()
            
            # Navigate to the post
            self.driver.get(url)
            self.random_delay()
            
            # Scroll to load comments
            self.scroll_to_load_comments()
            
            # Extract comments
            comments = self.extract_comments()
            
            if comments:
                # Save to CSV
                csv_path = self.save_to_csv(comments)
                logger.info(f"Successfully scraped {len(comments)} comments")
                return csv_path
            else:
                logger.warning("No comments found")
                return None
                
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver closed")


def main():
    """Main function for testing"""
    # Test URL
    test_url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    scraper = InstagramCommentScraper(headless=False)
    result = scraper.scrape_post(test_url)
    
    if result:
        print(f"✅ Comments successfully scraped and saved to: {result}")
    else:
        print("❌ Failed to scrape comments")


if __name__ == "__main__":
    main()
