"""
Instagram Comment Scraper
Scrapes comments and usernames from Instagram posts/reels and exports to CSV
"""

import os
import csv
import time
import random
import logging
import re
from datetime import datetime
from typing import List, Dict, Optional

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

import config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class InstagramCommentScraper:
    def __init__(self, headless: bool = config.HEADLESS):
        """Initialize the Instagram comment scraper"""
        self.driver = None
        self.wait = None
        self.headless = headless
        self.comments_data = []
        
        # Create output directory
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        
        if self.headless:
            chrome_options.add_argument('--headless')
            
        # Anti-detection measures
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # Set window size
        chrome_options.add_argument(f'--window-size={config.WINDOW_SIZE[0]},{config.WINDOW_SIZE[1]}')
        
        # Random user agent
        user_agent = random.choice(config.USER_AGENTS)
        chrome_options.add_argument(f'--user-agent={user_agent}')
        
        # Initialize driver
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # Set implicit wait
        self.driver.implicitly_wait(config.IMPLICIT_WAIT)
        self.wait = WebDriverWait(self.driver, config.IMPLICIT_WAIT)
        
        logger.info("WebDriver setup completed")
        
    def random_delay(self):
        """Add random delay to avoid detection"""
        delay = random.uniform(config.MIN_DELAY, config.MAX_DELAY)
        time.sleep(delay)
        
    def scroll_to_load_comments(self):
        """Scroll down to load more comments"""
        logger.info("Scrolling to load comments...")
        
        for i in range(config.MAX_SCROLLS):
            # Scroll down
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            self.random_delay()
            
            # Try to click "Load more comments" button if it exists
            try:
                # Look for various "Load more" button patterns
                load_more_selectors = [
                    'button[aria-label*="Load more"]',
                    'button[aria-label*="View more"]',
                    'button:contains("Load more")',
                    'button:contains("View more")',
                    'div[role="button"]:contains("Load more")'
                ]

                for selector in load_more_selectors:
                    try:
                        load_more_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if load_more_btn.is_displayed():
                            load_more_btn.click()
                            logger.info(f"Clicked 'Load more comments' button (scroll {i+1})")
                            time.sleep(config.COMMENT_LOAD_WAIT)
                            break
                    except:
                        continue
            except Exception:
                pass
                
            # Check if we've reached the end
            current_height = self.driver.execute_script("return document.body.scrollHeight")
            if i > 0 and current_height == getattr(self, 'last_height', 0):
                logger.info("Reached end of comments")
                break
            self.last_height = current_height
            
        logger.info(f"Completed {i+1} scrolls")
        
    def extract_comments_from_page_source(self, page_source: str) -> List[Dict[str, str]]:
        """Extract comments using BeautifulSoup from page source"""
        comments = []
        try:
            soup = BeautifulSoup(page_source, 'html.parser')

            # Look for JSON data in script tags that might contain comments
            script_tags = soup.find_all('script', type='application/json')
            for script in script_tags:
                try:
                    import json
                    data = json.loads(script.string)
                    # This is a simplified approach - Instagram's JSON structure is complex
                    # We'll look for text patterns that might be comments
                    json_str = json.dumps(data)

                    # Extract potential usernames and comments using regex
                    username_pattern = r'"username":"([^"]+)"'
                    text_pattern = r'"text":"([^"]+)"'

                    usernames = re.findall(username_pattern, json_str)
                    texts = re.findall(text_pattern, json_str)

                    # Try to pair usernames with texts
                    for i, username in enumerate(usernames):
                        if i < len(texts) and len(texts[i]) > 5:
                            comments.append({
                                'username': username,
                                'username_link': f"https://instagram.com/{username}",
                                'comment': texts[i],
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })

                except:
                    continue

        except Exception as e:
            logger.debug(f"Error parsing with BeautifulSoup: {e}")

        return comments

    def extract_comments(self) -> List[Dict[str, str]]:
        """Extract comments and usernames from the page"""
        logger.info("Extracting comments...")
        comments = []

        try:
            # Wait for page to load
            time.sleep(config.COMMENT_LOAD_WAIT)

            # Get page source for BeautifulSoup parsing
            page_source = self.driver.page_source

            # Try BeautifulSoup approach first
            bs_comments = self.extract_comments_from_page_source(page_source)
            if bs_comments:
                logger.info(f"Found {len(bs_comments)} comments using BeautifulSoup")
                comments.extend(bs_comments)

            # Approach 1: Look for specific Instagram comment patterns
            try:
                # Look for elements that contain both username links and comment text
                # Instagram often uses specific patterns for comments

                # Find all anchor tags that might be usernames
                all_links = self.driver.find_elements(By.TAG_NAME, "a")
                logger.info(f"Found {len(all_links)} total links")

                potential_usernames = []
                for link in all_links:
                    try:
                        href = link.get_attribute("href")
                        text = link.text.strip()

                        # Check if this looks like a username link
                        if (href and text and
                            'instagram.com/' in href and
                            len(text) > 0 and len(text) < 30 and
                            not any(skip in href for skip in ['/p/', '/reel/', '/tv/', '/explore/', '/accounts/'])):

                            # Extract username from URL
                            username = href.split('/')[-1] if href.endswith('/') else href.split('/')[-1]
                            if username and len(username) < 30:
                                potential_usernames.append({
                                    'element': link,
                                    'username': username,
                                    'link': href,
                                    'text': text
                                })

                    except Exception as e:
                        continue

                logger.info(f"Found {len(potential_usernames)} potential username links")

                # For each username, try to find associated comment text
                for user_data in potential_usernames:
                    try:
                        username_element = user_data['element']
                        username = user_data['username']
                        user_link = user_data['link']

                        # Look for comment text near the username
                        # Try to find parent containers that might hold the comment
                        parent = username_element
                        comment_text = ""

                        for level in range(5):  # Check up to 5 parent levels
                            try:
                                parent = parent.find_element(By.XPATH, "..")

                                # Look for spans that might contain comment text
                                spans = parent.find_elements(By.TAG_NAME, "span")
                                for span in spans:
                                    span_text = span.text.strip()
                                    if (span_text and
                                        len(span_text) > 5 and
                                        span_text != username and
                                        span_text != user_data['text'] and
                                        not any(skip in span_text.lower() for skip in
                                               ['reply', 'like', 'view', 'follow', 'ago', 'hours', 'minutes', 'days'])):
                                        comment_text = span_text
                                        break

                                if comment_text:
                                    break

                            except:
                                break

                        # If we found a comment, add it
                        if comment_text:
                            comments.append({
                                'username': username,
                                'username_link': user_link,
                                'comment': comment_text,
                                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })

                    except Exception as e:
                        logger.debug(f"Error processing username {user_data.get('username', 'unknown')}: {e}")
                        continue

            except Exception as e:
                logger.debug(f"Error in main extraction approach: {e}")

            # Remove duplicates
            seen_comments = set()
            unique_comments = []

            for comment in comments:
                comment_id = f"{comment['username']}:{comment['comment'][:50]}"
                if comment_id not in seen_comments:
                    # Additional filtering
                    if (len(comment['comment']) > 3 and
                        not any(skip in comment['comment'].lower() for skip in
                               ['follow', 'instagram', 'meta', 'english', 'español', 'contact uploading', 'see more'])):
                        unique_comments.append(comment)
                        seen_comments.add(comment_id)

            comments = unique_comments

        except Exception as e:
            logger.error(f"Error extracting comments: {e}")

        logger.info(f"Extracted {len(comments)} comments with usernames")
        return comments
        
    def save_to_csv(self, comments: List[Dict[str, str]], filename: str = None):
        """Save comments to CSV file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"instagram_comments_{timestamp}.csv"

        filepath = os.path.join(config.OUTPUT_DIR, filename)

        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['username', 'username_link', 'comment', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for comment in comments:
                    # Ensure all required fields are present
                    row = {
                        'username': comment.get('username', 'Unknown'),
                        'username_link': comment.get('username_link', ''),
                        'comment': comment.get('comment', ''),
                        'timestamp': comment.get('timestamp', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                    }
                    writer.writerow(row)

            logger.info(f"Comments saved to {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
            
    def scrape_post(self, url: str) -> Optional[str]:
        """Main method to scrape comments from an Instagram post"""
        logger.info(f"Starting to scrape: {url}")
        
        try:
            # Setup driver
            self.setup_driver()
            
            # Navigate to the post
            self.driver.get(url)
            self.random_delay()
            
            # Scroll to load comments
            self.scroll_to_load_comments()
            
            # Extract comments
            comments = self.extract_comments()
            
            if comments:
                # Save to CSV
                csv_path = self.save_to_csv(comments)
                logger.info(f"Successfully scraped {len(comments)} comments")
                return csv_path
            else:
                logger.warning("No comments found")
                return None
                
        except Exception as e:
            logger.error(f"Error during scraping: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver closed")


def main():
    """Main function for testing"""
    # Test URL
    test_url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    scraper = InstagramCommentScraper(headless=False)
    result = scraper.scrape_post(test_url)
    
    if result:
        print(f"✅ Comments successfully scraped and saved to: {result}")
    else:
        print("❌ Failed to scrape comments")


if __name__ == "__main__":
    main()
