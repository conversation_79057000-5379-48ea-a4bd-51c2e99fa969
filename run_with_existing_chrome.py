#!/usr/bin/env python3
"""
Run Instagram scraper with existing Chrome session
This keeps you logged in to Instagram
"""

import sys
import os
import subprocess
import time
from instagram_scraper_clean import InstagramCommentScraperClean

def check_chrome_debug():
    """Check if Chrome is running with remote debugging"""
    try:
        import requests
        response = requests.get("http://127.0.0.1:9222/json", timeout=2)
        return response.status_code == 200
    except:
        return False

def start_chrome_debug():
    """Start Chrome with remote debugging if not already running"""
    if check_chrome_debug():
        return True
    
    print("🚀 Starting Chrome with remote debugging...")
    try:
        # Try to run the Chrome debug starter
        result = subprocess.run([sys.executable, "start_chrome_debug.py"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            time.sleep(3)  # Wait for Chrome to start
            return check_chrome_debug()
        else:
            print(f"❌ Failed to start Chrome: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error starting Chrome: {e}")
        return False

def main():
    """Main function"""
    # Default URL if none provided
    default_url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    # Get URL from command line argument or use default
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = default_url
        
    print("🚀 Instagram Comment Scraper - Existing Chrome Mode")
    print(f"📱 Target URL: {url}")
    print("=" * 60)
    
    # Check if Chrome debug is available
    if not check_chrome_debug():
        print("⚠️  Chrome remote debugging not detected")
        print("🔧 Attempting to start Chrome with debugging enabled...")
        
        if not start_chrome_debug():
            print("\n❌ Could not start Chrome with remote debugging")
            print("\n📋 Manual setup instructions:")
            print("1. Close all Chrome windows")
            print("2. Start Chrome from terminal with:")
            print("   chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome_debug")
            print("3. Log in to Instagram")
            print("4. Run this script again")
            print("\n🔄 Falling back to new Chrome session...")
            
            # Use new Chrome session as fallback
            scraper = InstagramCommentScraperClean(headless=False, use_existing_chrome=False)
        else:
            print("✅ Chrome started! Please log in to Instagram and then press Enter...")
            input("Press Enter when you're logged in to Instagram...")
            scraper = InstagramCommentScraperClean(headless=False, use_existing_chrome=True)
    else:
        print("✅ Chrome remote debugging detected!")
        print("🔐 Using your existing Chrome session (you should stay logged in)")
        scraper = InstagramCommentScraperClean(headless=False, use_existing_chrome=True)
    
    try:
        # Run the scraper
        print(f"\n🔍 Starting to scrape: {url}")
        result = scraper.scrape_post(url)
        
        if result:
            print(f"\n✅ SUCCESS!")
            print(f"📄 Comments saved to: {result}")
            
            # Show some statistics
            if os.path.exists(result):
                import csv
                with open(result, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    comments = list(reader)
                    
                print(f"📊 Total comments scraped: {len(comments)}")
                
                # Show sample comments
                if comments:
                    print(f"\n📝 Sample comments:")
                    for i, comment in enumerate(comments[:5]):
                        username = comment['username']
                        text = comment['comment'][:80] + "..." if len(comment['comment']) > 80 else comment['comment']
                        print(f"  {i+1}. @{username}: {text}")
                        
                    if len(comments) > 5:
                        print(f"  ... and {len(comments) - 5} more comments")
                        
        else:
            print("❌ FAILED: No comments were scraped")
            print("💡 Make sure you're logged in to Instagram and the post is accessible")
            
    except KeyboardInterrupt:
        print("\n⏹️  Scraping interrupted by user")
    except Exception as e:
        print(f"❌ ERROR: {e}")
        print("💡 Check the logs for more details")
    
    print("\n🏁 Scraping completed!")
    print("💡 You can keep Chrome open for future scraping sessions")

if __name__ == "__main__":
    main()
