2025-06-06 15:39:38,864 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN/
2025-06-06 15:39:38,864 - INFO - ====== WebDriver manager ======
2025-06-06 15:39:38,993 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 15:39:39,060 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 15:39:39,114 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 15:39:40,191 - INFO - WebDriver setup completed
2025-06-06 15:39:46,422 - INFO - Looking for scrollable comment container...
2025-06-06 15:39:46,432 - WARNING - No suitable comment container found
2025-06-06 15:39:46,432 - ERROR - Could not find comment container
2025-06-06 15:39:46,530 - INFO - WebDriver closed
2025-06-06 15:41:36,157 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN/
2025-06-06 15:41:36,157 - INFO - ====== WebDriver manager ======
2025-06-06 15:41:36,281 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 15:41:36,348 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 15:41:36,407 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 15:41:37,498 - INFO - WebDriver setup completed
2025-06-06 15:41:43,497 - INFO - Looking for scrollable comment container...
2025-06-06 15:41:43,508 - INFO - Found comment container: 114 spans, 49 links
2025-06-06 15:41:43,508 - INFO - Container size: 1685 scroll / 400 client
2025-06-06 15:41:43,508 - INFO - Scrolling comment container to load all comments...
2025-06-06 15:41:45,530 - INFO - Initial container scroll height: 1685
2025-06-06 15:41:47,564 - INFO - Scroll 1: position=320, height=1685
2025-06-06 15:41:59,618 - INFO - Scroll 2: position=640, height=1685
2025-06-06 15:42:11,680 - INFO - Scroll 3: position=960, height=1685
2025-06-06 15:42:23,723 - INFO - Scroll 4: position=1280, height=1685
2025-06-06 15:42:35,787 - INFO - Scroll 5: position=1396, height=1796
2025-06-06 15:42:47,835 - INFO - Scroll 6: position=1396, height=1796
2025-06-06 15:42:57,849 - INFO - Reached end of container after 6 scrolls
2025-06-06 15:42:57,854 - INFO - Final container scroll height: 1796 (loaded 111 more pixels)
2025-06-06 15:42:57,854 - INFO - Extracting comments from container...
2025-06-06 15:42:57,869 - INFO - Found 52 total links in container
2025-06-06 15:42:58,209 - INFO - Found 19 potential username links
2025-06-06 15:43:19,729 - INFO - Extracted 18 comments from container
2025-06-06 15:43:19,730 - INFO - Comments saved to output/instagram_comments_clean_20250606_154319.csv
2025-06-06 15:43:19,730 - INFO - Successfully scraped 18 unique comments
2025-06-06 15:43:19,938 - INFO - WebDriver closed
2025-06-06 16:02:14,302 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN
2025-06-06 16:02:14,302 - INFO - ====== WebDriver manager ======
2025-06-06 16:02:14,450 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:02:15,570 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:02:15,632 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:02:16,889 - INFO - WebDriver setup completed
2025-06-06 16:02:24,856 - INFO - Looking for scrollable comment container...
2025-06-06 16:02:24,868 - INFO - Found comment container: 114 spans, 49 links
2025-06-06 16:02:24,868 - INFO - Container size: 1685 scroll / 400 client
2025-06-06 16:02:24,869 - INFO - Scrolling comment container to load all comments...
2025-06-06 16:02:26,886 - INFO - Initial container scroll height: 1685
2025-06-06 16:02:28,918 - INFO - Scroll 1: position=320, height=1685
2025-06-06 16:02:41,008 - INFO - Scroll 2: position=640, height=1685
2025-06-06 16:02:53,056 - INFO - Scroll 3: position=960, height=1685
2025-06-06 16:03:05,135 - INFO - Scroll 4: position=1280, height=1685
2025-06-06 16:03:17,210 - INFO - Scroll 5: position=1386, height=1787
2025-06-06 16:03:29,280 - INFO - Scroll 6: position=1386, height=1787
2025-06-06 16:03:39,327 - INFO - Reached end of container after 6 scrolls
2025-06-06 16:03:39,331 - INFO - Final container scroll height: 1787 (loaded 102 more pixels)
2025-06-06 16:03:39,331 - INFO - Extracting comments from container...
2025-06-06 16:03:39,347 - INFO - Found 52 total links in container
2025-06-06 16:03:39,678 - INFO - Found 19 potential username links
2025-06-06 16:04:01,232 - INFO - Extracted 18 comments from container
2025-06-06 16:04:01,233 - INFO - Comments saved to output/instagram_comments_clean_20250606_160401.csv
2025-06-06 16:04:01,233 - INFO - Successfully scraped 18 unique comments
2025-06-06 16:04:01,448 - INFO - WebDriver closed
2025-06-06 16:07:04,793 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN/
2025-06-06 16:07:04,793 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:07:04,793 - INFO - ====== WebDriver manager ======
2025-06-06 16:07:04,920 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:07:04,992 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:07:05,052 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:07:05,427 - INFO - ✅ Connected to existing Chrome session!
2025-06-06 16:07:11,806 - INFO - Looking for scrollable comment container...
2025-06-06 16:07:11,816 - INFO - Found comment container: 114 spans, 49 links
2025-06-06 16:07:11,816 - INFO - Container size: 1685 scroll / 400 client
2025-06-06 16:07:11,816 - INFO - Scrolling comment container to load all comments...
2025-06-06 16:07:13,837 - INFO - Initial container scroll height: 1685
2025-06-06 16:07:15,871 - INFO - Scroll 1: position=320, height=1685
2025-06-06 16:07:21,378 - ERROR - Error scrolling container: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
0   chromedriver                        0x0000000104aea654 cxxbridge1$str$ptr + 2723108
1   chromedriver                        0x0000000104ae28c8 cxxbridge1$str$ptr + 2690968
2   chromedriver                        0x0000000104636714 cxxbridge1$string$len + 90428
3   chromedriver                        0x000000010463c3c8 cxxbridge1$string$len + 114160
4   chromedriver                        0x000000010463e998 cxxbridge1$string$len + 123840
5   chromedriver                        0x00000001046bfafc cxxbridge1$string$len + 652580
6   chromedriver                        0x00000001046bede8 cxxbridge1$string$len + 649232
7   chromedriver                        0x00000001046719c8 cxxbridge1$string$len + 332784
8   chromedriver                        0x0000000104aae278 cxxbridge1$str$ptr + 2476360
9   chromedriver                        0x0000000104ab150c cxxbridge1$str$ptr + 2489308
10  chromedriver                        0x0000000104a8fa64 cxxbridge1$str$ptr + 2351412
11  chromedriver                        0x0000000104ab1d94 cxxbridge1$str$ptr + 2491492
12  chromedriver                        0x0000000104a80d58 cxxbridge1$str$ptr + 2290728
13  chromedriver                        0x0000000104ad1d60 cxxbridge1$str$ptr + 2622512
14  chromedriver                        0x0000000104ad1eec cxxbridge1$str$ptr + 2622908
15  chromedriver                        0x0000000104ae2514 cxxbridge1$str$ptr + 2690020
16  libsystem_pthread.dylib             0x000000019aceef94 _pthread_start + 136
17  libsystem_pthread.dylib             0x000000019ace9d34 thread_start + 8

2025-06-06 16:07:21,378 - INFO - Extracting comments from container...
2025-06-06 16:07:21,384 - ERROR - Error extracting from container: Message: stale element reference: stale element not found in the current frame
  (Session info: chrome=137.0.7151.69); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception
Stacktrace:
0   chromedriver                        0x0000000104aea654 cxxbridge1$str$ptr + 2723108
1   chromedriver                        0x0000000104ae28c8 cxxbridge1$str$ptr + 2690968
2   chromedriver                        0x0000000104636714 cxxbridge1$string$len + 90428
3   chromedriver                        0x000000010463c3c8 cxxbridge1$string$len + 114160
4   chromedriver                        0x000000010463e710 cxxbridge1$string$len + 123192
5   chromedriver                        0x000000010463e7b8 cxxbridge1$string$len + 123360
6   chromedriver                        0x000000010467d390 cxxbridge1$string$len + 380344
7   chromedriver                        0x0000000104672f94 cxxbridge1$string$len + 338364
8   chromedriver                        0x00000001046bede8 cxxbridge1$string$len + 649232
9   chromedriver                        0x00000001046719c8 cxxbridge1$string$len + 332784
10  chromedriver                        0x0000000104aae278 cxxbridge1$str$ptr + 2476360
11  chromedriver                        0x0000000104ab150c cxxbridge1$str$ptr + 2489308
12  chromedriver                        0x0000000104a8fa64 cxxbridge1$str$ptr + 2351412
13  chromedriver                        0x0000000104ab1d94 cxxbridge1$str$ptr + 2491492
14  chromedriver                        0x0000000104a80d58 cxxbridge1$str$ptr + 2290728
15  chromedriver                        0x0000000104ad1d60 cxxbridge1$str$ptr + 2622512
16  chromedriver                        0x0000000104ad1eec cxxbridge1$str$ptr + 2622908
17  chromedriver                        0x0000000104ae2514 cxxbridge1$str$ptr + 2690020
18  libsystem_pthread.dylib             0x000000019aceef94 _pthread_start + 136
19  libsystem_pthread.dylib             0x000000019ace9d34 thread_start + 8

2025-06-06 16:07:21,384 - INFO - Extracted 0 comments from container
2025-06-06 16:07:21,385 - WARNING - No comments found
2025-06-06 16:07:21,398 - INFO - WebDriver closed
2025-06-06 16:07:37,395 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN
2025-06-06 16:07:37,395 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:07:37,395 - INFO - ====== WebDriver manager ======
2025-06-06 16:07:37,474 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:07:37,537 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:07:37,600 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:07:43,312 - INFO - ✅ Connected to existing Chrome session!
2025-06-06 16:07:49,604 - INFO - Looking for scrollable comment container...
2025-06-06 16:07:49,615 - INFO - Found comment container: 114 spans, 49 links
2025-06-06 16:07:49,615 - INFO - Container size: 1685 scroll / 400 client
2025-06-06 16:07:49,616 - INFO - Scrolling comment container to load all comments...
2025-06-06 16:07:51,526 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(54, 'Connection reset by peer')': /session/114ed4a798c45094ae39869157d5162e
2025-06-06 16:07:51,527 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10204ccd0>: Failed to establish a new connection: [Errno 61] Connection refused')': /session/114ed4a798c45094ae39869157d5162e
2025-06-06 16:07:51,528 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x10204d590>: Failed to establish a new connection: [Errno 61] Connection refused')': /session/114ed4a798c45094ae39869157d5162e
2025-06-06 16:07:51,528 - INFO - WebDriver closed
2025-06-06 16:09:00,416 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN
2025-06-06 16:09:00,416 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:09:00,416 - INFO - ====== WebDriver manager ======
2025-06-06 16:09:00,495 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:09:00,554 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:09:00,609 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:09:00,999 - INFO - ✅ Connected to existing Chrome session!
2025-06-06 16:09:07,315 - INFO - Looking for scrollable comment container...
2025-06-06 16:09:07,335 - INFO - Found comment container: 205 spans, 45 links
2025-06-06 16:09:07,336 - INFO - Container size: 1619 scroll / 378 client
2025-06-06 16:09:07,336 - INFO - Scrolling comment container to load all comments...
2025-06-06 16:09:09,354 - INFO - Initial container scroll height: 1619
2025-06-06 16:09:11,391 - INFO - Scroll 1: position=302, height=1619
2025-06-06 16:09:23,465 - INFO - Scroll 2: position=605, height=1619
2025-06-06 16:09:35,545 - INFO - Scroll 3: position=908, height=1619
2025-06-06 16:09:47,635 - INFO - Scroll 4: position=1210, height=2522
2025-06-06 16:09:59,683 - INFO - Scroll 5: position=1512, height=2522
2025-06-06 16:10:11,726 - INFO - Scroll 6: position=1815, height=2522
2025-06-06 16:10:23,768 - INFO - Scroll 7: position=2078, height=2456
2025-06-06 16:10:35,809 - INFO - Scroll 8: position=2078, height=2456
2025-06-06 16:10:45,827 - INFO - Reached end of container after 8 scrolls
2025-06-06 16:10:45,832 - INFO - Final container scroll height: 2456 (loaded 837 more pixels)
2025-06-06 16:10:45,832 - INFO - Extracting comments from container...
2025-06-06 16:10:45,849 - INFO - Found 75 total links in container
2025-06-06 16:10:46,314 - INFO - Found 26 potential username links
2025-06-06 16:10:58,522 - INFO - Extracted 1 comments from container
2025-06-06 16:10:58,523 - INFO - Comments saved to output/instagram_comments_clean_20250606_161058.csv
2025-06-06 16:10:58,523 - INFO - Successfully scraped 1 unique comments
2025-06-06 16:10:58,535 - INFO - WebDriver closed
2025-06-06 16:12:25,317 - INFO - Starting to scrape: https://www.instagram.com/reel/DKcj0VdORgN
2025-06-06 16:12:25,317 - INFO - Attempting to connect to existing Chrome session...
2025-06-06 16:12:25,317 - INFO - ====== WebDriver manager ======
2025-06-06 16:12:25,397 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:12:25,455 - INFO - Get LATEST chromedriver version for google-chrome
2025-06-06 16:12:25,519 - INFO - Driver [/Users/<USER>/.wdm/drivers/chromedriver/mac64/137.0.7151.68/chromedriver-mac-arm64/chromedriver] found in cache
2025-06-06 16:12:25,902 - INFO - ✅ Connected to existing Chrome session!
2025-06-06 16:12:32,695 - INFO - Looking for scrollable comment container...
2025-06-06 16:12:32,713 - INFO - Found comment container: 203 spans, 45 links
2025-06-06 16:12:32,713 - INFO - Container size: 1601 scroll / 378 client
2025-06-06 16:12:32,713 - INFO - Scrolling comment container to load all comments...
2025-06-06 16:12:34,724 - INFO - Initial container scroll height: 1601
2025-06-06 16:12:36,751 - INFO - Scroll 1: position=302, height=1601
2025-06-06 16:12:48,805 - INFO - Scroll 2: position=605, height=1601
2025-06-06 16:13:00,869 - INFO - Scroll 3: position=908, height=1601
2025-06-06 16:13:12,956 - INFO - Scroll 4: position=1210, height=2354
2025-06-06 16:13:25,031 - INFO - Scroll 5: position=1512, height=2354
2025-06-06 16:13:37,071 - INFO - Scroll 6: position=1815, height=2354
2025-06-06 16:13:49,166 - INFO - Scroll 7: position=1976, height=2522
2025-06-06 16:14:01,268 - INFO - Scroll 8: position=2078, height=2456
2025-06-06 16:14:13,320 - INFO - Scroll 9: position=2078, height=2456
2025-06-06 16:14:23,381 - INFO - Reached end of container after 9 scrolls
2025-06-06 16:14:23,385 - INFO - Final container scroll height: 2456 (loaded 855 more pixels)
2025-06-06 16:14:23,385 - INFO - Extracting comments from container...
2025-06-06 16:14:23,401 - INFO - Found 75 total links in container
2025-06-06 16:14:23,886 - INFO - Found 26 potential username links
2025-06-06 16:14:36,139 - INFO - Extracted 1 comments from container
2025-06-06 16:14:36,140 - INFO - Comments saved to output/instagram_comments_clean_20250606_161436.csv
2025-06-06 16:14:36,140 - INFO - Successfully scraped 1 unique comments
2025-06-06 16:14:36,151 - INFO - WebDriver closed
