#!/usr/bin/env python3
"""
Test script to detect Instagram comment containers
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def setup_driver():
    """Setup Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    return driver

def find_scrollable_containers(driver):
    """Find all potentially scrollable containers"""
    print("🔍 Looking for scrollable containers...")
    
    # Get all div elements
    all_divs = driver.find_elements(By.TAG_NAME, "div")
    print(f"Found {len(all_divs)} div elements")
    
    scrollable_containers = []
    
    for i, div in enumerate(all_divs):
        try:
            # Get computed styles
            overflow_x = driver.execute_script("return window.getComputedStyle(arguments[0]).overflowX", div)
            overflow_y = driver.execute_script("return window.getComputedStyle(arguments[0]).overflowY", div)
            
            # Check if scrollable
            if overflow_x in ['auto', 'scroll'] or overflow_y in ['auto', 'scroll']:
                scroll_height = driver.execute_script("return arguments[0].scrollHeight", div)
                client_height = driver.execute_script("return arguments[0].clientHeight", div)
                
                if scroll_height > client_height:
                    scrollable_containers.append({
                        'index': i,
                        'element': div,
                        'overflow_x': overflow_x,
                        'overflow_y': overflow_y,
                        'scroll_height': scroll_height,
                        'client_height': client_height,
                        'class': div.get_attribute('class'),
                        'id': div.get_attribute('id')
                    })
                    
        except Exception as e:
            continue
    
    return scrollable_containers

def test_container_scrolling(driver, container_info):
    """Test scrolling within a container"""
    container = container_info['element']
    print(f"\n📜 Testing container {container_info['index']}:")
    print(f"   Class: {container_info['class']}")
    print(f"   Overflow X/Y: {container_info['overflow_x']}/{container_info['overflow_y']}")
    print(f"   Scroll/Client Height: {container_info['scroll_height']}/{container_info['client_height']}")
    
    # Try scrolling
    initial_scroll = driver.execute_script("return arguments[0].scrollTop", container)
    print(f"   Initial scroll position: {initial_scroll}")
    
    # Scroll down
    driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", container)
    time.sleep(1)
    
    final_scroll = driver.execute_script("return arguments[0].scrollTop", container)
    print(f"   Final scroll position: {final_scroll}")
    
    if final_scroll > initial_scroll:
        print("   ✅ Container is scrollable!")
        return True
    else:
        print("   ❌ Container did not scroll")
        return False

def main():
    """Main test function"""
    url = "https://www.instagram.com/reel/DKcj0VdORgN/"
    
    print("🚀 Starting Instagram Container Detection Test")
    print(f"📱 Target URL: {url}")
    print("=" * 60)
    
    driver = setup_driver()
    
    try:
        # Navigate to the post
        print("📂 Loading Instagram post...")
        driver.get(url)
        time.sleep(5)  # Wait for page to load
        
        # Find scrollable containers
        containers = find_scrollable_containers(driver)
        
        print(f"\n📊 Found {len(containers)} scrollable containers")
        
        if containers:
            print("\n🧪 Testing each container:")
            for container_info in containers[:5]:  # Test first 5 containers
                scrollable = test_container_scrolling(driver, container_info)
                if scrollable:
                    print(f"   🎯 This container might contain comments!")
                    
                    # Try to find text content in this container
                    spans = container_info['element'].find_elements(By.TAG_NAME, "span")
                    print(f"   📝 Found {len(spans)} span elements in this container")
                    
                    if len(spans) > 10:  # Likely contains comments
                        print("   🎉 This container likely contains comments!")
                        
                        # Show some sample text
                        for j, span in enumerate(spans[:3]):
                            try:
                                text = span.text.strip()
                                if len(text) > 5:
                                    print(f"      Sample text {j+1}: {text[:50]}...")
                            except:
                                continue
                    
                print()
        else:
            print("❌ No scrollable containers found")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        driver.quit()
        print("🏁 Test completed!")

if __name__ == "__main__":
    main()
